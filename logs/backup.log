set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250324_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-24 18:00:03.522336
Config  : ./westside.local/private/backups/20250324_180002-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250324_180002-westside_local-database.sql.gz         6.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250325_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-25 12:00:03.250976
Config  : ./westside.local/private/backups/20250325_120002-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250325_120002-westside_local-database.sql.gz         6.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250325_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-25 18:00:02.924323
Config  : ./westside.local/private/backups/20250325_180002-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250325_180002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250326_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-26 12:00:02.294529
Config  : ./westside.local/private/backups/20250326_120001-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250326_120001-westside_local-database.sql.gz         3.5MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250326_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-26 18:00:03.436812
Config  : ./westside.local/private/backups/20250326_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250326_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250327_000001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-27 00:00:03.547731
Config  : ./westside.local/private/backups/20250327_000001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250327_000001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250327_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-27 12:00:03.694044
Config  : ./westside.local/private/backups/20250327_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250327_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250327_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-27 18:00:02.367600
Config  : ./westside.local/private/backups/20250327_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250327_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250328_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-28 12:00:02.548428
Config  : ./westside.local/private/backups/20250328_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250328_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250401_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-01 12:00:03.643387
Config  : ./westside.local/private/backups/20250401_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250401_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250401_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-01 18:00:02.171862
Config  : ./westside.local/private/backups/20250401_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250401_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250402_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-02 12:00:02.983726
Config  : ./westside.local/private/backups/20250402_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250402_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250403_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-03 12:00:02.916227
Config  : ./westside.local/private/backups/20250403_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250403_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250403_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-03 18:00:03.070232
Config  : ./westside.local/private/backups/20250403_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250403_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250404_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-04 12:00:03.863257
Config  : ./westside.local/private/backups/20250404_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250404_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250404_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-04 18:00:03.726916
Config  : ./westside.local/private/backups/20250404_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250404_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250407_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-07 12:00:02.738555
Config  : ./westside.local/private/backups/20250407_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250407_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250407_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-07 18:00:03.317285
Config  : ./westside.local/private/backups/20250407_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250407_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250408_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-08 12:00:03.408753
Config  : ./westside.local/private/backups/20250408_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250408_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250408_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-08 18:00:03.219445
Config  : ./westside.local/private/backups/20250408_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250408_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250409_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-09 12:00:03.696998
Config  : ./westside.local/private/backups/20250409_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250409_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250409_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-09 18:00:03.899861
Config  : ./westside.local/private/backups/20250409_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250409_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250410_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-10 12:00:03.695082
Config  : ./westside.local/private/backups/20250410_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250410_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250410_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-10 18:00:03.178933
Config  : ./westside.local/private/backups/20250410_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250410_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250411_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-11 12:00:03.010929
Config  : ./westside.local/private/backups/20250411_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250411_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250415_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-15 12:00:02.677074
Config  : ./westside.local/private/backups/20250415_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250415_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250415_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-15 18:00:02.888031
Config  : ./westside.local/private/backups/20250415_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250415_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250416_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-16 12:00:02.729975
Config  : ./westside.local/private/backups/20250416_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250416_120001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250416_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-16 18:00:03.030519
Config  : ./westside.local/private/backups/20250416_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250416_180001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250417_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-17 12:00:02.809532
Config  : ./westside.local/private/backups/20250417_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250417_120001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250417_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-17 18:00:02.618259
Config  : ./westside.local/private/backups/20250417_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250417_180001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250421_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-21 12:00:03.779313
Config  : ./westside.local/private/backups/20250421_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250421_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250422_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-22 12:00:03.147594
Config  : ./westside.local/private/backups/20250422_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250422_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250422_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-22 18:00:03.640496
Config  : ./westside.local/private/backups/20250422_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250422_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250423_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-23 12:00:02.542753
Config  : ./westside.local/private/backups/20250423_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250423_120001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250423_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-23 18:00:02.949137
Config  : ./westside.local/private/backups/20250423_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250423_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250424_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-24 12:00:04.111774
Config  : ./westside.local/private/backups/20250424_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250424_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250424_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-24 18:00:03.057792
Config  : ./westside.local/private/backups/20250424_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250424_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250425_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-25 00:00:04.319444
Config  : ./westside.local/private/backups/20250425_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250425_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250425_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-25 12:00:03.574962
Config  : ./westside.local/private/backups/20250425_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250425_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250428_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-28 00:00:04.271445
Config  : ./westside.local/private/backups/20250428_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250428_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250428_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-28 12:00:02.603856
Config  : ./westside.local/private/backups/20250428_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250428_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250428_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-28 18:00:02.996554
Config  : ./westside.local/private/backups/20250428_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250428_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250429_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-29 12:00:03.308828
Config  : ./westside.local/private/backups/20250429_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250429_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250429_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-29 18:00:03.389785
Config  : ./westside.local/private/backups/20250429_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250429_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250430_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-30 12:00:02.885785
Config  : ./westside.local/private/backups/20250430_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250430_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250430_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-30 18:00:03.208678
Config  : ./westside.local/private/backups/20250430_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250430_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250502_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-02 12:00:02.687215
Config  : ./westside.local/private/backups/20250502_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250502_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250502_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-02 18:00:02.885869
Config  : ./westside.local/private/backups/20250502_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250502_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250505_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-05 12:00:04.689831
Config  : ./westside.local/private/backups/20250505_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250505_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250505_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-05 18:00:02.896719
Config  : ./westside.local/private/backups/20250505_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250505_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250506_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-06 00:00:06.333786
Config  : ./westside.local/private/backups/20250506_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250506_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250506_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-06 12:00:02.875756
Config  : ./westside.local/private/backups/20250506_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250506_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250506_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-06 18:00:02.313038
Config  : ./westside.local/private/backups/20250506_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250506_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250507_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-07 12:00:03.909755
Config  : ./westside.local/private/backups/20250507_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250507_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250507_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-07 18:00:02.427556
Config  : ./westside.local/private/backups/20250507_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250507_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250508_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-08 00:00:03.862277
Config  : ./westside.local/private/backups/20250508_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250508_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250508_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-08 12:00:03.209603
Config  : ./westside.local/private/backups/20250508_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250508_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250508_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-08 18:00:03.185048
Config  : ./westside.local/private/backups/20250508_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250508_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250509_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-09 12:00:03.818950
Config  : ./westside.local/private/backups/20250509_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250509_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250512_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-12 12:00:02.744943
Config  : ./westside.local/private/backups/20250512_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250512_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250512_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-12 18:00:03.102186
Config  : ./westside.local/private/backups/20250512_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250512_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250513_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-13 12:00:02.682830
Config  : ./westside.local/private/backups/20250513_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250513_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250513_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-13 18:00:02.791368
Config  : ./westside.local/private/backups/20250513_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250513_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250514_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-14 12:00:03.050558
Config  : ./westside.local/private/backups/20250514_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250514_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250514_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-14 18:00:03.187745
Config  : ./westside.local/private/backups/20250514_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250514_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250515_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-15 12:00:03.305384
Config  : ./westside.local/private/backups/20250515_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250515_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250515_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-15 18:00:02.862069
Config  : ./westside.local/private/backups/20250515_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250515_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250516_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-16 12:00:03.721441
Config  : ./westside.local/private/backups/20250516_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250516_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250520_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-20 12:00:03.661465
Config  : ./westside.local/private/backups/20250520_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250520_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250520_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-20 18:00:02.244216
Config  : ./westside.local/private/backups/20250520_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250520_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250521_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-21 12:00:02.298308
Config  : ./westside.local/private/backups/20250521_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250521_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250521_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-21 18:00:02.358365
Config  : ./westside.local/private/backups/20250521_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250521_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250522_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-22 12:00:02.530822
Config  : ./westside.local/private/backups/20250522_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250522_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250522_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-22 18:00:02.203642
Config  : ./westside.local/private/backups/20250522_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250522_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250523_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-23 12:00:02.773992
Config  : ./westside.local/private/backups/20250523_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250523_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250526_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-26 12:00:03.576753
Config  : ./westside.local/private/backups/20250526_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250526_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250526_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-26 18:00:03.656923
Config  : ./westside.local/private/backups/20250526_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250526_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250527_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-27 12:00:03.731160
Config  : ./westside.local/private/backups/20250527_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250527_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250527_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-27 18:00:04.302006
Config  : ./westside.local/private/backups/20250527_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250527_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250528_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-28 12:00:03.443708
Config  : ./westside.local/private/backups/20250528_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250528_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250529_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-29 12:00:03.533644
Config  : ./westside.local/private/backups/20250529_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250529_120002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250529_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-29 18:00:03.339335
Config  : ./westside.local/private/backups/20250529_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250529_180002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250530_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-30 12:00:03.169977
Config  : ./westside.local/private/backups/20250530_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250530_120002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250602_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-02 12:00:03.470820
Config  : ./westside.local/private/backups/20250602_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250602_120002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250602_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-02 18:00:03.033869
Config  : ./westside.local/private/backups/20250602_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250602_180001-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250603_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-03 12:00:02.724016
Config  : ./westside.local/private/backups/20250603_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250603_120001-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250603_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-03 18:00:04.045817
Config  : ./westside.local/private/backups/20250603_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250603_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250604_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-04 12:00:05.515364
Config  : ./westside.local/private/backups/20250604_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250604_120002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250604_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-04 18:00:05.493939
Config  : ./westside.local/private/backups/20250604_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250604_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250605_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-05 12:00:06.149809
Config  : ./westside.local/private/backups/20250605_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250605_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250605_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-05 18:00:05.346172
Config  : ./westside.local/private/backups/20250605_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250605_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250609_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-09 12:00:03.440678
Config  : ./westside.local/private/backups/20250609_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250609_120001-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250609_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-09 18:00:04.011020
Config  : ./westside.local/private/backups/20250609_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250609_180002-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250610_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-10 12:00:04.044089
Config  : ./westside.local/private/backups/20250610_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250610_120002-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250610_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-10 18:00:04.150597
Config  : ./westside.local/private/backups/20250610_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250610_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250611_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-11 12:00:03.965847
Config  : ./westside.local/private/backups/20250611_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250611_120002-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250611_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-11 18:00:03.126721
Config  : ./westside.local/private/backups/20250611_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250611_180001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250612_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-12 12:00:03.627492
Config  : ./westside.local/private/backups/20250612_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250612_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250612_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-12 18:00:04.018568
Config  : ./westside.local/private/backups/20250612_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250612_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250613_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-13 12:00:03.984494
Config  : ./westside.local/private/backups/20250613_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250613_120002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250613_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-13 18:00:05.480642
Config  : ./westside.local/private/backups/20250613_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250613_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250616_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-16 12:00:03.956813
Config  : ./westside.local/private/backups/20250616_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250616_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250616_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-16 18:00:05.247769
Config  : ./westside.local/private/backups/20250616_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250616_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250617_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-17 12:00:03.972212
Config  : ./westside.local/private/backups/20250617_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250617_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250617_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-17 18:00:05.266903
Config  : ./westside.local/private/backups/20250617_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250617_180002-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250618_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-18 12:00:04.431854
Config  : ./westside.local/private/backups/20250618_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250618_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250618_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-18 18:00:03.247809
Config  : ./westside.local/private/backups/20250618_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250618_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250619_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-19 12:00:03.803018
Config  : ./westside.local/private/backups/20250619_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250619_120002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250619_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-19 18:00:03.772706
Config  : ./westside.local/private/backups/20250619_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250619_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250620_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-20 12:00:06.016724
Config  : ./westside.local/private/backups/20250620_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250620_120002-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250621_000001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-21 00:00:04.170547
Config  : ./westside.local/private/backups/20250621_000001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250621_000001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250623_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-23 12:00:03.512621
Config  : ./westside.local/private/backups/20250623_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250623_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250623_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-23 18:00:03.932613
Config  : ./westside.local/private/backups/20250623_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250623_180002-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250624_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-24 12:00:04.943631
Config  : ./westside.local/private/backups/20250624_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250624_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250624_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-24 18:00:03.627287
Config  : ./westside.local/private/backups/20250624_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250624_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250625_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-25 12:00:03.422880
Config  : ./westside.local/private/backups/20250625_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250625_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250625_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-25 18:00:04.071289
Config  : ./westside.local/private/backups/20250625_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250625_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250626_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-26 12:00:03.429088
Config  : ./westside.local/private/backups/20250626_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250626_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250626_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-26 18:00:04.127443
Config  : ./westside.local/private/backups/20250626_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250626_180002-westside_local-database.sql.gz         8.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250627_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-27 12:00:03.801828
Config  : ./westside.local/private/backups/20250627_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250627_120002-westside_local-database.sql.gz         6.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250630_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-30 12:00:03.272208
Config  : ./westside.local/private/backups/20250630_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250630_120001-westside_local-database.sql.gz         6.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250630_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-30 18:00:03.064987
Config  : ./westside.local/private/backups/20250630_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250630_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250701_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-01 12:00:05.040814
Config  : ./westside.local/private/backups/20250701_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250701_120002-westside_local-database.sql.gz         6.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250701_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-01 18:00:02.921457
Config  : ./westside.local/private/backups/20250701_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250701_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250702_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-02 12:00:03.783650
Config  : ./westside.local/private/backups/20250702_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250702_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250702_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-02 18:00:03.542607
Config  : ./westside.local/private/backups/20250702_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250702_180002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250703_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-03 12:00:03.442817
Config  : ./westside.local/private/backups/20250703_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250703_120001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250703_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-03 18:00:04.011987
Config  : ./westside.local/private/backups/20250703_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250703_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250704_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-04 12:00:03.430023
Config  : ./westside.local/private/backups/20250704_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250704_120001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250707_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-07 12:00:03.658823
Config  : ./westside.local/private/backups/20250707_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250707_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250707_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-07 18:00:03.220622
Config  : ./westside.local/private/backups/20250707_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250707_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250708_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-08 12:00:04.376584
Config  : ./westside.local/private/backups/20250708_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250708_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250708_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-08 18:00:03.735526
Config  : ./westside.local/private/backups/20250708_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250708_180002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250709_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-09 12:00:04.434987
Config  : ./westside.local/private/backups/20250709_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250709_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250710_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-10 12:00:03.111732
Config  : ./westside.local/private/backups/20250710_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250710_120001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250710_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-10 18:00:03.329812
Config  : ./westside.local/private/backups/20250710_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250710_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250711_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-11 12:00:05.139629
Config  : ./westside.local/private/backups/20250711_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250711_120002-westside_local-database.sql.gz         13.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250711_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-11 18:00:05.096619
Config  : ./westside.local/private/backups/20250711_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250711_180001-westside_local-database.sql.gz         13.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250714_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-14 12:00:04.555418
Config  : ./westside.local/private/backups/20250714_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250714_120002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250714_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-14 18:00:03.806982
Config  : ./westside.local/private/backups/20250714_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250714_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250716_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-16 12:00:04.076401
Config  : ./westside.local/private/backups/20250716_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250716_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250716_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-16 18:00:04.373228
Config  : ./westside.local/private/backups/20250716_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250716_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250717_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-17 12:00:04.017488
Config  : ./westside.local/private/backups/20250717_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250717_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250717_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-17 18:00:04.161241
Config  : ./westside.local/private/backups/20250717_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250717_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250718_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-18 12:00:04.974215
Config  : ./westside.local/private/backups/20250718_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250718_120002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250721_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-21 12:00:04.538036
Config  : ./westside.local/private/backups/20250721_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250721_120001-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250721_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-21 18:00:06.650678
Config  : ./westside.local/private/backups/20250721_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250721_180002-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250723_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-23 12:00:05.261827
Config  : ./westside.local/private/backups/20250723_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250723_120002-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250723_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-23 18:00:04.062150
Config  : ./westside.local/private/backups/20250723_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250723_180001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250724_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-24 12:00:05.693602
Config  : ./westside.local/private/backups/20250724_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250724_120001-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250724_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-24 18:00:05.042475
Config  : ./westside.local/private/backups/20250724_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250724_180001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250725_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-25 12:00:06.148376
Config  : ./westside.local/private/backups/20250725_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250725_120001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250725_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-25 18:00:04.944163
Config  : ./westside.local/private/backups/20250725_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250725_180002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250728_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-28 12:00:04.458644
Config  : ./westside.local/private/backups/20250728_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250728_120001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250728_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-28 18:00:05.960358
Config  : ./westside.local/private/backups/20250728_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250728_180002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250729_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-29 12:00:04.417817
Config  : ./westside.local/private/backups/20250729_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250729_120002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250729_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-29 18:00:06.489100
Config  : ./westside.local/private/backups/20250729_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250729_180002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250731_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-31 12:00:04.361525
Config  : ./westside.local/private/backups/20250731_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250731_120001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250801_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-01 12:00:05.811865
Config  : ./westside.local/private/backups/20250801_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250801_120002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250801_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-01 18:00:05.068696
Config  : ./westside.local/private/backups/20250801_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250801_180001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250804_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-04 12:00:03.776157
Config  : ./westside.local/private/backups/20250804_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250804_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250804_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-04 18:00:03.654579
Config  : ./westside.local/private/backups/20250804_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250804_180001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250805_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-05 12:00:04.836466
Config  : ./westside.local/private/backups/20250805_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250805_120002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250805_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-05 18:00:05.863336
Config  : ./westside.local/private/backups/20250805_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250805_180002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250806_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-06 12:00:05.434822
Config  : ./westside.local/private/backups/20250806_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250806_120002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250806_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-06 18:00:06.103645
Config  : ./westside.local/private/backups/20250806_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250806_180001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250807_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-07 12:00:04.294562
Config  : ./westside.local/private/backups/20250807_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250807_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250807_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-07 18:00:06.611005
Config  : ./westside.local/private/backups/20250807_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250807_180002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250808_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-08 12:00:04.450279
Config  : ./westside.local/private/backups/20250808_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250808_120002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250808_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-08 18:00:04.397895
Config  : ./westside.local/private/backups/20250808_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250808_180002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250811_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-11 12:00:05.814990
Config  : ./westside.local/private/backups/20250811_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250811_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250811_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-11 18:00:03.888090
Config  : ./westside.local/private/backups/20250811_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250811_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250812_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-12 12:00:04.905757
Config  : ./westside.local/private/backups/20250812_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250812_120002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250812_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-12 18:00:04.543068
Config  : ./westside.local/private/backups/20250812_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250812_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250813_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-13 12:00:04.205130
Config  : ./westside.local/private/backups/20250813_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250813_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250813_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-13 18:00:04.544141
Config  : ./westside.local/private/backups/20250813_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250813_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250814_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-14 12:00:05.371779
Config  : ./westside.local/private/backups/20250814_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250814_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250818_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-18 12:00:04.280955
Config  : ./westside.local/private/backups/20250818_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250818_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250818_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-18 18:00:04.500500
Config  : ./westside.local/private/backups/20250818_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250818_180002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250819_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-19 12:00:04.757795
Config  : ./westside.local/private/backups/20250819_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250819_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250819_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-19 18:00:03.815190
Config  : ./westside.local/private/backups/20250819_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250819_180001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250820_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-20 12:00:04.337659
Config  : ./westside.local/private/backups/20250820_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250820_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250820_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-20 18:00:05.944513
Config  : ./westside.local/private/backups/20250820_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250820_180001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250822_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-22 12:00:08.477424
Config  : ./westside.local/private/backups/20250822_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250822_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250822_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-22 18:00:03.729149
Config  : ./westside.local/private/backups/20250822_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250822_180001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250826_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-26 12:00:04.956263
Config  : ./westside.local/private/backups/20250826_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250826_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250826_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-26 18:00:04.583768
Config  : ./westside.local/private/backups/20250826_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250826_180002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250827_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-27 12:00:06.067594
Config  : ./westside.local/private/backups/20250827_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250827_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250828_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-28 12:00:04.520665
Config  : ./westside.local/private/backups/20250828_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250828_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250828_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-28 18:00:04.902788
Config  : ./westside.local/private/backups/20250828_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250828_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250831_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-31 12:00:05.927346
Config  : ./westside.local/private/backups/20250831_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250831_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250831_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-31 18:00:06.594690
Config  : ./westside.local/private/backups/20250831_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250831_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250901_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-01 12:00:04.380719
Config  : ./westside.local/private/backups/20250901_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250901_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250901_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-01 18:00:04.512279
Config  : ./westside.local/private/backups/20250901_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250901_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250902_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-02 12:00:05.145794
Config  : ./westside.local/private/backups/20250902_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250902_120002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250902_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-02 18:00:05.140498
Config  : ./westside.local/private/backups/20250902_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250902_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250903_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-03 12:00:03.775775
Config  : ./westside.local/private/backups/20250903_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250903_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250908_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-08 12:00:05.924568
Config  : ./westside.local/private/backups/20250908_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250908_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250908_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-08 18:00:04.389337
Config  : ./westside.local/private/backups/20250908_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250908_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250909_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-09 12:00:04.177019
Config  : ./westside.local/private/backups/20250909_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250909_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250909_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-09 18:00:04.291389
Config  : ./westside.local/private/backups/20250909_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250909_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250910_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-10 12:00:03.840807
Config  : ./westside.local/private/backups/20250910_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250910_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250910_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-10 18:00:05.940986
Config  : ./westside.local/private/backups/20250910_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250910_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250911_060002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-11 06:00:04.430893
Config  : ./westside.local/private/backups/20250911_060002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250911_060002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250911_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-11 12:00:04.248963
Config  : ./westside.local/private/backups/20250911_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250911_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250911_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-11 18:00:03.789899
Config  : ./westside.local/private/backups/20250911_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250911_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250912_120148-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-12 12:01:53.197433
Config  : ./westside.local/private/backups/20250912_120148-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250912_120148-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250913_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-13 12:00:05.820803
Config  : ./westside.local/private/backups/20250913_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250913_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
