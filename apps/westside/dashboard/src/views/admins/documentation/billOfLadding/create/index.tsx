import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  PlusCircle,
  Minus,
  FileText,
  X,
  Image,
  ChevronLast,
} from "lucide-react";
import {
  createBillOfLading,
  multipleFileUploadBol,
  readOcrBillOfLading,
} from "@/services/admin/billOfLading";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";

const BillOfLadingCreate = () => {
  const navigate = useNavigate();

  // form state
  const [formData, setFormData] = useState({
    bol_number: "",
    message_status: "",
    document_type: "",
    issuingOffice: "",
    carrier: "",
    carrier_booking_number: "",
    shipper: "",
    inttra_si_number: "",
    main_vessel: "",
    main_voyage: "",
    sail_date: "",
    place_of_receipt: "",
    place_of_delivery: "",
    port_of_load: "",
    port_of_discharge: "",
    letter_of_credit_number: "",
    export_license_issue: "",
    export_license_expiry: "",
    total_equipment: "",
    total_gross_weight: "",
    total_packages: "",
  });

  const [containers, setContainers] = useState([
    {
      equipment_name: "",
      description: "",
      shipper_seal_number: "",
      carrier_seal_number: "",
      cargo: "",
      cargo_weight: "",
    },
  ]);

  const [attachments, setAttachments] = useState<File[]>([
    undefined as unknown as File,
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  // OCR popup state
  const [ocrOpen, setOcrOpen] = useState(false);
  const [ocrFile, setOcrFile] = useState<File | null>(null);
  const [ocrLoading, setOcrLoading] = useState(false);

  const handleOcrUpload = async () => {
  if (!ocrFile) {
    toast.error("Please upload a file first");
    return;
  }

  setOcrLoading(true); // start loader
  try {
    const formData1 = new FormData();
    formData1.append("file", ocrFile);

    console.log("Uploading to OCR:", ocrFile);
    const ocrRes = await readOcrBillOfLading(formData1);
    console.log("OCR Response:", ocrRes);

    if(ocrRes?.status === 'success'){
      toast.success("File sent for OCR. Processing may take some time.");
      setOcrOpen(false);
      setOcrFile(null);
      ocrRes?.data?.bol_doc_name &&
        navigate(`/dashboard/documentation/my-bill-of-ladding/${ocrRes?.data?.bol_doc_name}`); 
    } else {
      toast.error("Failed to process OCR. Please try again.");
    }
  } catch (err) {
    toast.error("Error sending file for OCR");
  } finally {
    setOcrLoading(false);
  }
};

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleContainerChange = (index, e) => {
    const { name, value } = e.target;
    setContainers((prev) => {
      const updated = [...prev];
      updated[index][name] = value;
      return updated;
    });
  };

  const addContainer = () => {
    setContainers((prev) => [
      ...prev,
      {
        equipment_name: "",
        description: "",
        shipper_seal_number: "",
        carrier_seal_number: "",
        cargo: "",
        cargo_weight: "",
      },
    ]);
  };

  const removeContainer = (index) => {
    setContainers((prev) => prev.filter((_, i) => i !== index));
  };

  const handleAttachmentChange = (e, index) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setAttachments((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
    e.target.value = "";
  };

  const handleAddAttachment = () => {
    setAttachments((prev) => [...prev, undefined as unknown as File]);
  };
  const handleSubmit1 = () => {
    console.log("Submit form data:", formData, containers, attachments);
  };
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      const res = await createBillOfLading({
        ...formData,
        equipment: containers,
      });

      if (res?.status_code !== 200) {
        toast.error("Failed to create Bill of Lading");
        return;
      }

      toast.success("Bill of Lading created successfully");
      const bolId = res?.data?.id;

      if (
        attachments.filter((file) => file instanceof File).length > 0 &&
        bolId
      ) {
        const uploadData = new FormData();
        attachments
          .filter((file) => file instanceof File)
          .forEach((file) => uploadData.append("attachment", file));
        uploadData.append("bol_id", bolId);
        await multipleFileUploadBol(uploadData);
      }

      navigate(-1);
    } catch (err) {
      toast.error("Error creating Bill of Lading");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 pt-6">
      {/* <h1 className="text-xl font-semibold">Create Bill of Lading</h1> */}
      <div className="flex gap-4 mt-6 justify-end">
        <Button
          type="submit"
          variant={"secondary"}
          className="h-11 px-5 bg-red-600 text-white hover:bg-orange-500 "
          onClick={() => setOcrOpen(true)}
        >
          Read OCR
          <FileText className="ml-1 w-5 h-5" />
        </Button>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <Input
          name="bol_number"
          placeholder="BL Number"
          value={formData.bol_number}
          onChange={handleChange}
        />
        <Input
          name="message_status"
          placeholder="Status"
          value={formData.message_status}
          onChange={handleChange}
        />
        <Input
          name="document_type"
          placeholder="Document Type"
          value={formData.document_type}
          onChange={handleChange}
        />
        <Input
          name="issuingOffice"
          placeholder="Issuing Office"
          value={formData.issuingOffice}
          onChange={handleChange}
        />
        <Input
          name="carrier"
          placeholder="Carrier"
          value={formData.carrier}
          onChange={handleChange}
        />
        <Input
          name="carrier_booking_number"
          placeholder="Carrier Booking Number"
          value={formData.carrier_booking_number}
          onChange={handleChange}
        />
        <Input
          name="shipper"
          placeholder="Shipper"
          value={formData.shipper}
          onChange={handleChange}
        />
        <Input
          name="inttra_si_number"
          placeholder="INTTRA SI Number"
          value={formData.inttra_si_number}
          onChange={handleChange}
        />
        <Input
          name="main_vessel"
          placeholder="Main Vessel"
          value={formData.main_vessel}
          onChange={handleChange}
        />
        <Input
          name="main_voyage"
          placeholder="Main Voyage"
          value={formData.main_voyage}
          onChange={handleChange}
        />
        <Input
          type="date"
          name="sail_date"
          value={formData.sail_date}
          onChange={handleChange}
        />
        <Input
          name="place_of_receipt"
          placeholder="Place of Receipt"
          value={formData.place_of_receipt}
          onChange={handleChange}
        />
        <Input
          name="place_of_delivery"
          placeholder="Place of Delivery"
          value={formData.place_of_delivery}
          onChange={handleChange}
        />
        <Input
          name="port_of_load"
          placeholder="Port of Load"
          value={formData.port_of_load}
          onChange={handleChange}
        />
        <Input
          name="port_of_discharge"
          placeholder="Port of Discharge"
          value={formData.port_of_discharge}
          onChange={handleChange}
        />
        <Input
          name="letter_of_credit_number"
          placeholder="Letter of Credit Ref"
          value={formData.letter_of_credit_number}
          onChange={handleChange}
        />
        <Input
          type="date"
          name="export_license_issue"
          value={formData.export_license_issue}
          onChange={handleChange}
        />
        <Input
          type="date"
          name="export_license_expiry"
          value={formData.export_license_expiry}
          onChange={handleChange}
        />
        <Input
          name="total_equipment"
          placeholder="Total Equipments"
          value={formData.total_equipment}
          onChange={handleChange}
        />
        <Input
          name="total_gross_weight"
          placeholder="Total Gross Weight"
          value={formData.total_gross_weight}
          onChange={handleChange}
        />
        <Input
          name="total_packages"
          placeholder="Total Packages"
          value={formData.total_packages}
          onChange={handleChange}
        />
      </div>

      {/* Container Details */}
      <div className="p-4 border rounded-lg">
        <h2 className="font-semibold mb-2">Container Details</h2>

        {/* Labels Row */}
        <div className="grid grid-cols-7 gap-2 mb-2 text-sm font-medium text-gray-600 pt-4">
          <span>Container Number</span>
          <span>Container Type</span>
          <span>Shipper Seal</span>
          <span>Carrier Seal</span>
          <span>HS Code</span>
          <span>Cargo Weight</span>
          <span className="text-center"></span>
        </div>

        {containers.map((container, idx) => (
          <div key={idx} className="grid grid-cols-7 gap-2 mb-3 items-center">
            <Input
              name="equipment_name"
              placeholder="Container Number"
              value={container.equipment_name}
              onChange={(e) => handleContainerChange(idx, e)}
            />
            <Input
              name="description"
              placeholder="Container Type"
              value={container.description}
              onChange={(e) => handleContainerChange(idx, e)}
            />
            <Input
              name="shipper_seal_number"
              placeholder="Shipper Seal"
              value={container.shipper_seal_number}
              onChange={(e) => handleContainerChange(idx, e)}
            />
            <Input
              name="carrier_seal_number"
              placeholder="Carrier Seal"
              value={container.carrier_seal_number}
              onChange={(e) => handleContainerChange(idx, e)}
            />
            <Input
              name="cargo"
              placeholder="HS Code"
              value={container.cargo}
              onChange={(e) => handleContainerChange(idx, e)}
            />
            <Input
              name="cargo_weight"
              placeholder="Cargo Weight"
              value={container.cargo_weight}
              onChange={(e) => handleContainerChange(idx, e)}
            />

            {containers.length > 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => removeContainer(idx)}
                className="justify-self-center"
              >
                <Minus />
              </Button>
            )}
          </div>
        ))}
        <div className="flex justify-end">
          <Button variant="outline" size="sm" onClick={addContainer}>
            <PlusCircle className="w-4 h-4 mr-2" /> Add Container
          </Button>
        </div>
      </div>

      {/* File Upload Section */}
      <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">Upload b/l</h2>

        {attachments.map((file, index) => (
          <div key={index} className="flex w-1/2 items-center space-x-2 mb-3">
            <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
              <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                <Image className="w-5 h-5 text-gray-600" />
                <span className="font-medium">CHOOSE FILE</span>
                <input
                  type="file"
                  className="hidden"
                  onChange={(e) => handleAttachmentChange(e, index)}
                />
              </label>
              <div className="flex-1 flex items-center justify-between bg-gray-50 px-4 py-2 text-sm">
                <span className="truncate text-gray-700">
                  {file?.name || "Upload Attach File Here...."}
                </span>
                {file && (
                  <button
                    type="button"
                    onClick={() => {
                      const updated = [...attachments];
                      updated[index] = undefined as unknown as File;
                      setAttachments(updated);
                    }}
                    className="text-gray-500 hover:text-red-600 ml-2"
                    title="Remove file"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {attachments.length > 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setAttachments(attachments.filter((_, i) => i !== index))
                }
              >
                <Minus />
              </Button>
            )}

            {index === attachments.length - 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={handleAddAttachment}
              >
                <PlusCircle />
              </Button>
            )}
          </div>
        ))}
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={() => navigate(-1)}>
          Cancel
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? "Creating..." : "Create BL"}
        </Button>
      </div>
      {/* ocr popup */}
      <Dialog open={ocrOpen} onOpenChange={setOcrOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Upload File</DialogTitle>
          </DialogHeader>

          <div className="flex items-center gap-3">
            <label className="flex items-center gap-2 px-3 py-2 border rounded-md cursor-pointer bg-white">
              <Image className="w-5 h-5 text-gray-600" />
              <span className="text-sm">Choose File</span>
              <input
                type="file"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) setOcrFile(file);
                }}
              />
            </label>
            <span className="truncate text-sm text-gray-700">
              {ocrFile?.name || "No file chosen"}
            </span>
            {ocrFile && (
              <button
                type="button"
                onClick={() => setOcrFile(null)}
                className="text-gray-500 hover:text-red-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setOcrOpen(false)}
              disabled={ocrLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleOcrUpload} disabled={ocrLoading}>
              {ocrLoading ? (
                <div className="flex items-center gap-2">
                  <svg
                    className="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    />
                  </svg>
                  <span>Processing OCR...</span>
                </div>
              ) : (
                "OK"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BillOfLadingCreate;
