import React, { useEffect, useState } from "react";
import {
  <PERSON>c<PERSON>,
  Edit3,
  ThumbsUp,
  FileText,
  Image,
  Minus,
  X,
  Plus,
  PlusCircle,
  Trash2,
} from "lucide-react";
import {
  fetchBillOfLadingList,
  fetchBillOfLadingPdf,
  fetchBillOfLadingDetails,
  multipleFileUploadBol,
  deleteUploadedFile,
} from "@/services/admin/billOfLading";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/Loader";
import { useParams } from "react-router-dom";
import { toast } from "sonner";
import {
  formatDate,
  formatDateTime,
  formatDateTimeToGMT,
} from "@/utils/functions";
import dayjs from "dayjs";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { ViewAttachedFiles } from "@/components/ui/preview";

const BillOfLadingWorkspace = () => {
  const { id } = useParams();

  const navigate = useNavigate();

  // First query to get the list
  const {
    data: billOfLadingData,
    isLoading: isListLoading,
    refetch: refetchBillOfLading,
  } = useQuery({
    queryKey: ["fetchBillOfLadingDetails", id],
    queryFn: () => fetchBillOfLadingDetails(id),
  });

  const selectedBillOfLading = billOfLadingData?.message[0] || null;

  useEffect(() => {
    if (selectedBillOfLading) {
      console.log(
        "selectedBillOfLading.bol_attachments",
        selectedBillOfLading.bol_attachments
      );
      setOldFileAttachments(selectedBillOfLading.bol_attachments || []);
    }
  }, [billOfLadingData]);
  const [attachments, setAttachments] = useState<File[]>([
    undefined as unknown as File,
  ]);
  const [oldFileAttachments, setOldFileAttachments] = useState<File[]>([]);
  const [removedOldFiles, setRemovedOldFiles] = useState<string[]>([]);

  const handleAttachmentChange = (e, index) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setAttachments((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
    e.target.value = "";
  };

  const handleAddAttachment = () => {
    setAttachments((prev) => [...prev, undefined as unknown as File]);
  };

  const [isDeleting, setIsDeleting] = useState(false);
  const handleDeleteOldFile = async (indexToRemove: number) => {
    const removedFile = oldFileAttachments[indexToRemove];

    try {
      setIsDeleting(true);
      if (id && removedFile?.file_url) {
        const res = await deleteUploadedFile(id, removedFile.file_name);

        if (res?.status_code === 200) {
          toast.success("File deleted successfully");
          setRemovedOldFiles((prev) => [...prev, removedFile?.file_url]);

          const updatedAttachments = oldFileAttachments.filter(
            (_, idx) => idx !== indexToRemove
          );
          setOldFileAttachments(updatedAttachments);
        } else {
          toast.error("File deletion failed");
        }
      }
    } catch (error) {
      toast.error("An error occurred while deleting the file");
    } finally {
      setIsDeleting(false);
    }
  };

  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<{
    name: string;
    license: string;
  } | null>(null);
  const handleFileClick = (file) => {
    setSelectedFile({
      name: file.file_name,
      license: "/files/" + file.file_name,
    });
    setPreviewOpen(true);
  };
  const [isUploading, setIsUploading] = useState(false);
  const submitFiles = async (files: File[]) => {
    if (files.length === 0) {
      toast.error("Please select files to upload");
      return;
    }
    if (!id) {
      toast.error("No Bill of Lading ID found");
      return;
    }

    const formData = new FormData();
    files.forEach((file) => {
      formData.append("attachment", file);
    });
    formData.append("bol_id", id);

    try {
      setIsUploading(true);
      const res = await multipleFileUploadBol(formData);
      if (res?.status_code === 200) {
        toast.success("Files uploaded successfully");
        refetchBillOfLading();
        setAttachments([undefined as unknown as File]);
      } else {
        toast.error("File upload failed");
      }
    } catch (error) {
      toast.error("File upload failed");
    } finally {
      setIsUploading(false);
    }
  };

  if (isListLoading) {
    return (
      <div className="flex justify-center items-center h-screen w-screen">
        <Loader />
      </div>
    );
  }
  if (!selectedBillOfLading) {
    return (
      <div className="flex justify-center items-center h-screen w-screen">
        <p className="text-red-500">Bill of Lading not found</p>
      </div>
    );
  }

  const inttraSiNumber = selectedBillOfLading.references?.find(
    (ref) => ref.reference_type === "InttraShippingInstructionId"
  );
  const inttraSiNumberText = inttraSiNumber?.text || "--";

  const shipperReferenceNumber = selectedBillOfLading.references?.find(
    (ref) => ref.reference_type === "ShipperReferenceNumber"
  );
  const shipperReferenceNumberText = shipperReferenceNumber?.text || "--";

  const shipperParty = selectedBillOfLading.parties?.find(
    (party) => party.partner_role === "Shipper"
  );
  const shipperName = shipperParty?.partner_name || "--";

  const handleDownload = async (fileUrl: string) => {
    try {
      // const blob = await fetchBillOfLadingPdf(fileName);

      // // Create a URL from the blob
      // const url = window.URL.createObjectURL(blob);

      // // Create an anchor element and click it
      // const link = document.createElement("a");
      // link.href = url;
      // link.setAttribute("download", fileName); // Set the download filename
      // document.body.appendChild(link);
      // link.click();

      // // Clean up
      // link.parentNode?.removeChild(link);
      // window.URL.revokeObjectURL(url);
      const fullUrl = `${`${import.meta.env.VITE_BACKEND_URL}/${fileUrl}`}`;
      window.open(fullUrl, "_blank");
    } catch (err) {
      console.error("Download failed", err);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-end gap-2">
        {/* <button className="bg-slate-800 text-white px-4 py-2 rounded flex items-center gap-2">
          {" "}
          <Paperclip className="w-4 h-4" />
          Archive BL
        </button>
        <button className="bg-slate-800 text-white px-4 py-2 rounded flex items-center gap-2">
          {" "}
          <Edit3 className="w-4 h-4" />
          Change Request
        </button>
        <button
          className="bg-white-800 text-grey px-4 py-2 border rounded flex items-center gap-2"
          disabled
        >
          {" "}
          <ThumbsUp className="w-4 h-4" />
          Approve BL
        </button> */}
        <button
          className="bg-slate-800 text-white px-4 py-2 rounded flex items-center gap-2"
          onClick={() => handleDownload(selectedBillOfLading.pdf_file_path)}
          disabled={!selectedBillOfLading.pdf_file_path}
        >
          {" "}
          <FileText className="w-4 h-4" />
          Download PDF
        </button>
      </div>
      <div className="p-6 bg-gray-50 rounded-lg shadow">
        {/* BL Details Section */}
        <div className="grid grid-cols-4 gap-4 text-md">
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              BL Number
            </p>
            <p>{selectedBillOfLading?.bol_number}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Status/Type
            </p>
            <p>
              {selectedBillOfLading?.message_status ?? "-"} /{" "}
              {selectedBillOfLading?.document_type ?? "-"}
            </p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Issuing Office
            </p>
            <p>{selectedBillOfLading?.issuingOffice ?? "--"}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Date Received
            </p>
            <p>
              {selectedBillOfLading?.create_date_time
                ? dayjs(selectedBillOfLading?.create_date_time).format(
                    "MMM-DD-YYYY hh:mm A"
                  )
                : ""}
            </p>
          </div>

          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">Carrier</p>
            <p>{selectedBillOfLading?.carrier ?? "--"}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Carrier Booking Number(s)
            </p>
            <p>{selectedBillOfLading?.carrier_booking_number ?? "--"}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">Shipper</p>
            <p>{shipperName}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              INTTRA SI Number
            </p>
            <p>{inttraSiNumberText}</p>
          </div>

          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Main Vessel
            </p>
            <p>{selectedBillOfLading?.main_vessel ?? "--"}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Main Voyage
            </p>
            <p>{selectedBillOfLading?.main_voyage ?? "--"}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Sail Date
            </p>
            <p>
              {selectedBillOfLading?.main_transport_sail_date
                ? dayjs(selectedBillOfLading?.main_transport_sail_date).format(
                    "MMM-DD-YYYY"
                  )
                : ""}
            </p>
          </div>

          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Place of Carrier Receipt
            </p>
            <p>
              {selectedBillOfLading?.place_of_receipt_location} (
              {selectedBillOfLading?.place_of_receipt ?? "--"})
            </p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Place of Carrier Delivery
            </p>
            <p>
              {selectedBillOfLading?.place_of_delivery_location} (
              {selectedBillOfLading?.place_of_delivery ?? "--"})
            </p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Port of Load
            </p>
            <p>
              {selectedBillOfLading?.port_of_load_location} (
              {selectedBillOfLading?.port_of_load ?? "--"})
            </p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Port of Discharge
            </p>
            <p>
              {selectedBillOfLading?.port_of_discharge_location} (
              {selectedBillOfLading?.port_of_discharge ?? "--"})
            </p>
          </div>

          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Shipper's Reference(s)
            </p>
            <p>{shipperReferenceNumberText}</p>
          </div>
          {/* <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Forwarder
            </p>
            <p>--</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Forwarder Reference(s)
            </p>
            <p>--</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Other Reference(s)
            </p>
            <p>--</p>
          </div> */}

          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Letter of Credit Reference
            </p>
            <p>{selectedBillOfLading?.letter_of_credit_number ?? "--"}</p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Issue Date
            </p>
            <p>
              {formatDateTime(selectedBillOfLading?.export_license_issue) ??
                "--"}
            </p>
          </div>
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Expiry Date
            </p>
            <p>
              {formatDateTime(selectedBillOfLading?.export_license_expiry) ??
                "--"}
            </p>
          </div>

          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Total Equipments
            </p>
            <p>{selectedBillOfLading?.total_equipment ?? "--"}</p>
          </div>
          {/* <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Total Gross Volume
            </p>
            <p>
              {selectedBillOfLading?.total_gross_volume ?? "--"}
            </p>
          </div> */}
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Total Gross Weight
            </p>
            <p>{selectedBillOfLading?.total_gross_weight ?? "--"}</p>
          </div>
          {/* <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Total Calculated Net Weight
            </p>
            <p>{selectedBillOfLading?.total_net_weight_caluclated ?? "--"}</p>
          </div> */}
          <div>
            <p className="font-semibold text-gray-500 text-sm pb-1">
              Total Packages
            </p>
            <p>{selectedBillOfLading?.total_packages ?? "--"}</p>
          </div>
        </div>

        {/* Container Table */}
        {/* <div>
          <h3 className="text-lg font-medium mt-6">Container Details</h3>
          <table className="w-full text-md border mt-6">
            <thead className="bg-[#EAECF1]">
              <tr>
                <th className="text-left p-2 border">Container Number</th>
                <th className="text-left p-2 border">Description</th>
                <th className="text-left p-2 border">Weight</th>
                <th className="text-left p-2 border">Shipper Seal Number</th>
                <th className="text-left p-2 border">Carrier Seal Number</th>
                <th className="text-left p-2 border">Cargo</th>
                <th className="text-left p-2 border">Cargo Weight</th>
              </tr>
            </thead>
            <tbody>
              {selectedBillOfLading?.equipment?.length > 0 ? (
                selectedBillOfLading?.equipment.map((container, index) => (
                  <tr key={index}>
                    <td className="p-2 border">
                      {container.equipment_name || "--"}
                    </td>
                    <td className="p-2 border">
                      {container.description || "--"}
                    </td>
                    <td className="p-2 border">
                      {container?.weight_value || "--"} {container?.weight_type || ""}
                    </td>
                    <td className="p-2 border">
                      {container?.shipper_seal_number || "--"}
                    </td>
                    <td className="p-2 border">
                      {container?.carrier_seal_number || "--"}
                    </td>
                    <td className="p-2 border">
                      {container?.cargo || "--"}
                    </td>
                    <td className="p-2 border">
                      {container?.cargo_weight || "--"}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td className="p-2 border" colSpan={7}>
                    No containers found
                  </td>
                </tr>
              )}
              
            </tbody>
          </table>
        </div> */}
      </div>
      {/* Upload Section */}
      <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">Upload files</h2>

        {/* New File Uploads */}
        {attachments.map((file, index) => (
          <div key={index} className="flex w-1/2 items-center space-x-2 mb-3">
            <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
              <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                <Image className="w-5 h-5 text-gray-600" />
                <span className="font-medium">CHOOSE FILE</span>
                <input
                  type="file"
                  className="hidden"
                  onChange={(e) => handleAttachmentChange(e, index)}
                />
              </label>
              <div className="flex-1 flex items-center justify-between bg-gray-50 px-4 py-2 text-sm">
                <span className="truncate text-gray-700">
                  {file?.name || "Upload Attach File Here...."}
                </span>
                {file && (
                  <button
                    type="button"
                    onClick={() => {
                      const updated = [...attachments];
                      updated[index] = undefined as unknown as File;
                      setAttachments(updated);
                    }}
                    className="text-gray-500 hover:text-red-600 ml-2"
                    title="Remove file"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Remove Button */}
            {attachments.length > 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setAttachments(attachments.filter((_, i) => i !== index))
                }
              >
                <Minus />
              </Button>
            )}
            {/* Add Button */}
            {index === attachments.length - 1 && (
              <Button
                variant="outline"
                size="icon"
                onClick={() =>
                  setAttachments([...attachments, undefined as unknown as File])
                }
              >
                <PlusCircle />
              </Button>
            )}
          </div>
        ))}

        {/* Upload Files Button */}
        {attachments.filter((file) => file instanceof File).length !== 0 && (
          <div className="flex w-1/2 justify-end mt-4">
            <button
              disabled={isUploading}
              className={`text-sm px-3 py-1 rounded flex items-center gap-2 
        ${
          isUploading
            ? "bg-gray-400 cursor-not-allowed"
            : "bg-slate-800 hover:bg-slate-900 text-white"
        }`}
              onClick={() =>
                submitFiles(attachments.filter((file) => file instanceof File))
              }
            >
              {isUploading ? (
                <>
                  <svg
                    className="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                  </svg>
                  Uploading...
                </>
              ) : (
                <>
                  <FileText className="w-4 h-4" />
                  Upload Files
                </>
              )}
            </button>
          </div>
        )}

        {/* Divider */}
        <hr className="my-5 border-gray-300" />

        {/* Old Files List */}
        {oldFileAttachments?.length > 0 && (
          <div className="border border-gray-200 rounded-lg p-4 bg-white">
            <h3 className="font-medium text-gray-800 mb-3">Existing Files</h3>
            <ul className="space-y-2">
              {oldFileAttachments.map((oldFile: any, index: number) => (
                <li
                  key={index}
                  className="flex items-center justify-between p-2 border border-gray-200 rounded hover:bg-gray-50"
                >
                  <span
                    className="text-blue-600 underline cursor-pointer truncate"
                    onClick={() => handleFileClick(oldFile)}
                  >
                    {oldFile?.file_name}
                  </span>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <button
                        type="button"
                        className="ml-2 text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          Are you sure you want to delete?
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently
                          delete <strong>{oldFile?.file_name}</strong>.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Not sure</AlertDialogCancel>
                        <AlertDialogAction
                          className="bg-red-600 hover:bg-red-600/90"
                          onClick={() => handleDeleteOldFile(index)}
                          disabled={isDeleting}
                        >
                          Sure, Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">Container Details</h2>
        <table className="w-full text-md border mt-6">
          <thead className="bg-[#EAECF1]">
            <tr>
              <th className="text-left p-2 border">Container Number</th>
              <th className="text-left p-2 border">Container Type</th>
              {/* <th className="text-left p-2 border">Weight</th> */}
              <th className="text-left p-2 border">Shipper Seal Number</th>
              <th className="text-left p-2 border">Carrier Seal Number</th>
              <th className="text-left p-2 border">HS Code</th>
              <th className="text-left p-2 border">Cargo Weight (KG)</th>
            </tr>
          </thead>
          <tbody>
            {selectedBillOfLading?.equipment?.length > 0 ? (
              selectedBillOfLading?.equipment.map((container, index) => (
                <tr key={index}>
                  <td className="p-2 border">
                    {container.equipment_name || "--"}
                  </td>
                  <td className="p-2 border">
                    {container.description || "--"}
                  </td>
                  {/* <td className="p-2 border">
                      {container?.weight_value || "--"} {container?.weight_type || ""}
                    </td> */}
                  <td className="p-2 border">
                    {container?.shipper_seal_number || "--"}
                  </td>
                  <td className="p-2 border">
                    {container?.carrier_seal_number || "--"}
                  </td>
                  <td className="p-2 border">{container?.cargo || "--"}</td>
                  <td className="p-2 border">
                    {container?.cargo_weight || "--"}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td className="p-2 border" colSpan={7}>
                  No containers found
                </td>
              </tr>
            )}
          </tbody>
        </table>

        <table className="w-full text-md border mt-6">
          <thead className="bg-[#EAECF1]">
            <tr>
              <th className="text-left p-2 border">Total Containers</th>
              <th className="text-left p-2 border">Total Gross Weight</th>
              <th className="text-left p-2 border">Total Net Weight</th>
            </tr>
          </thead>
          <tbody>
            {selectedBillOfLading?.total_net_weight_caluclated ? (
              <tr>
                <td className="p-2 border">
                  {selectedBillOfLading?.total_equipment || "--"}
                </td>
                <td className="p-2 border">
                  {selectedBillOfLading?.total_gross_weight || "--"}
                </td>
                <td className="p-2 border">
                  {selectedBillOfLading?.total_net_weight_caluclated || "--"}
                </td>
              </tr>
            ) : (
              <tr>
                <td className="p-2 border" colSpan={7}></td>
              </tr>
            )}
          </tbody>
        </table>
        <div className="mt-4 text-sm">
          <p className="text-red-500">
            {selectedBillOfLading?.weight_difference_warning
              ? `** ${selectedBillOfLading.weight_difference_warning}`
              : ""}
          </p>
        </div>
      </div>
      <div className="flex justify-end gap-2 pt-3">
        <div>
          <button
            onClick={() => navigate(-1)}
            className="mb-2 px-4 py-2 bg-gray-200 text-sm rounded hover:bg-gray-300 transition"
          >
            ← Back
          </button>
        </div>
      </div>

      <ViewAttachedFiles
        open={previewOpen}
        selectedFile={selectedFile}
        onOpenChange={setPreviewOpen}
        setSelectedFile={setSelectedFile}
      />
      {/* Documents Section */}
      {/* <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">Documents</h2>
        <table className="w-full text-md border">
          <thead className="bg-[#EAECF1]">
            <tr>
              <th className="text-left p-2 border">BL Creation Date (GMT)</th>
              <th className="text-left p-2 border">Type</th>
              <th className="text-left p-2 border">Carrier Stock Paper</th>
              <th className="text-left p-2 border">Carrier Comments</th>
              <th className="text-left p-2 border">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-2 border">{formatDateTimeToGMT(selectedBillOfLading?.create_date_time)}</td>
              <td className="p-2 border text-primary underline">
                Unrated / Copy
              </td>
              <td className="p-2 border">No</td>
              <td className="p-2 border text-red-600">
                Shipping Instruction Cut-Off Date: 08/31/2025 12:00
              </td>
              <td className="p-2 border">
                <button className="bg-slate-800 text-white text-sm px-3 py-1 rounded">
                  Share Document
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div className="text-right pt-3">
          <button className="bg-slate-800 text-white text-sm px-4 py-2 rounded">
            Add Note
          </button>
        </div>
      </div> */}

      {/* Customer Notes Section */}
      {/* <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">Customer Notes</h2>
        <table className="w-full text-md border">
          <thead className="bg-[#EAECF1]">
            <tr>
              <th className="text-left p-2 border">Date (GMT)</th>
              <th className="text-left p-2 border">Notes</th>
              <th className="text-left p-2 border">User ID</th>
              <th className="text-left p-2 border">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-2 border">08-Apr-2025 21:20:21</td>
              <td className="p-2 border text-primary underline">TEst Note</td>
              <td className="p-2 border">Newman</td>
              <td className="p-2 border">
                <button className="bg-slate-800 text-white text-sm px-3 py-1 rounded">
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div> */}

      {/* History Section */}
      {/* <div className="p-6 bg-gray-50 rounded-lg shadow">
        <h2 className="font-semibold text-lg mb-2">History</h2>
        <table className="w-full text-md border">
          <thead className="bg-[#EAECF1]">
            <tr>
              <th className="text-left p-2 border">Event</th>
              <th className="text-left p-2 border">Date Processed (GMT)</th>
              <th className="text-left p-2 border">Party</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="p-2 border text-primary underline">
                New Document
              </td>
              <td className="p-2 border">08-Apr-2025 13:04:21</td>
              <td className="p-2 border">CMA CGM</td>
            </tr>
            <tr>
              <td className="p-2 border">Routed BL ZIP File</td>
              <td className="p-2 border">08-Apr-2025 13:04:21</td>
              <td className="p-2 border">PDF Router</td>
            </tr>
            <tr>
              <td className="p-2 border text-primary underline">
                Change Request
              </td>
              <td className="p-2 border">08-Apr-2025 13:04:21</td>
              <td className="p-2 border">Vinu Kalliyar</td>
            </tr>
            <tr>
              <td className="p-2 border text-primary underline">
                Change Request
              </td>
              <td className="p-2 border">08-Apr-2025 13:04:21</td>
              <td className="p-2 border">Vinu Kalliyar</td>
            </tr>
          </tbody>
        </table>
      </div> */}
    </div>
  );
};

export default BillOfLadingWorkspace;
