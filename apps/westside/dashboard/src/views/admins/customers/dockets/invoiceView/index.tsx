// Converted from editable form to a view-only invoice page
import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Image, Save, FileText, Eye, Pen } from "lucide-react";
import {
  getInvoiceDetails,
  generateInvoicePDF,
  handleMakeInvoicePayment,
} from "@/services/admin/invoiceGenerate";
import { useParams } from "react-router-dom";
import { useQuery, useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { ViewAttachedFiles } from "./viewAttachedFile";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

const ViewInvoicePage = () => {
  // const attachments = [
  //   { name: "Attachment 1.pdf" },
  //   { name: "Attachment 2.jpg" },
  // ];
  const baseUrl = import.meta.env.VITE_BACKEND_URL || "";
  const navigate = useNavigate();
  const { id } = useParams();
  const {
    data: invoiceData,
    isFetching: isInvoiceFetching,
    refetch: refetchInvoice,
  } = useQuery({
    queryKey: ["getInvoiceDetails", { invoiceId: id }],
    queryFn: () => getInvoiceDetails(id as string),
    enabled: !!id,
  });

  const { mutate: triggerGeneratePDF, isPending: isGeneratingPDF } =
    useMutation({
      mutationFn: (docname: string) => generateInvoicePDF(docname),
      onSuccess: (res) => {
        if (res?.message?.status_code === 200) {
          toast.success(res?.message?.Message);
          refetchInvoice();
        } else {
          toast.error(res?.message?.error || "Failed to generate PDF");
        }
      },
      onError: (error) => {
        toast.error("An error occurred while generating the PDF");
        console.error(error);
      },
    });
  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<{
    name: string;
    license: string;
  } | null>(null);
  const handleFileClick = (file) => {
    setSelectedFile({
      name: file.file_name,
      license: file.file_url,
    });
    setPreviewOpen(true);
  };
  // Handler
  const handleGeneratePDF = (docname: string) => {
    if (docname) triggerGeneratePDF(docname);
  };

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPaymentType, setSelectedPaymentType] = useState("Cash");
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(
    null
  );
  const [notes, setNotes] = useState("");

  const handleChangeStatusInvoice = (
    invoiceId: string,
    payment_type: string
  ) => {
    if (invoiceId) {
      triggerChangeStatus({ invoiceId, payment_type });
    }
  };

  const { mutate: triggerChangeStatus, isPending: isChangeInvoiceStatus } =
    useMutation({
      mutationFn: ({
        invoiceId,
        payment_type,
      }: {
        invoiceId: string;
        payment_type: string;
        notes?: string;
      }) => handleMakeInvoicePayment(invoiceId, payment_type, notes),

      onSuccess: (res) => {
        if (res?.message?.status_code === "200") {
          toast.success(
            res?.message?.message ||
              "Invoice marked as paid in QuickBooks successfully."
          );
          refetchInvoice();
        } else {
          toast.error(res?.message?.error || "Failed to change status");
        }
      },

      onError: (error) => {
        toast.error("An error occurred while changing invoice status");
        console.error(error);
      },
    });

  if (isInvoiceFetching) return <div>Loading...</div>;

  return (
    <div className="p-1 space-y-6">
      <div className="p-1 grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Invoice Details */}
        <Card className="md:col-span-3 space-y-3 p-8">
          <h3 className="text-lg font-medium text-orange-600 tracking-wide mb-1">
            Invoice Details
          </h3>
          <hr className="border-gray-300 mt-0" />

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <strong>Customer Name:</strong>{" "}
              {invoiceData?.message?.data?.customer_name}
            </div>
            <div>
              <strong>Invoice Number:</strong>{" "}
              {invoiceData?.message?.data?.invoice_number}
            </div>

            <div>
              <strong>Invoice Date:</strong>{" "}
              {invoiceData?.message?.data?.invoice_date
                ? dayjs(invoiceData?.message?.data?.invoice_date).format(
                    "MMM-DD-YYYY"
                  )
                : "--"}
            </div>
            
            <div>
              <strong>Purchase order #:</strong>{" "}
              {invoiceData?.message?.data?.purchase_order_number}
            </div>
            <div>
              <strong>Booking #:</strong>{" "}
              {invoiceData?.message?.data?.booking_id}
            </div>
            <div>
              <strong>HS Code:</strong> {invoiceData?.message?.data?.hs_code}
            </div>
            <div>
              <strong>Origin Port:</strong>{" "}
              {invoiceData?.message?.data?.origin_port}
            </div>
            <div>
              <strong>Destination Port:</strong>{" "}
              {invoiceData?.message?.data?.destination_port}
            </div>
            <div>
              <strong>BOL #:</strong> {invoiceData?.message?.data?.bol}
            </div>
            <div>
              <strong>Shipping Date:</strong>{" "}
              {invoiceData?.message?.data?.shipping_date
                ? dayjs(invoiceData?.message?.data?.shipping_date).format(
                    "MMM-DD-YYYY"
                  )
                : "--"}
            </div>
            <div>
              <strong>Due Date:</strong>{" "}
              {invoiceData?.message?.data?.due_date
                ? dayjs(invoiceData?.message?.data?.due_date).format(
                    "MMM-DD-YYYY"
                  )
                : "--"}
            </div>
            <div>
              <strong>Incoterm:</strong> {invoiceData?.message?.data?.incoterm}
            </div>
          </div>

          {/* Items */}
          <h3 className="text-lg font-medium mt-6">Items</h3>
          <div className="w-full overflow-x-auto">
            <table className="min-w-[750px] text-sm w-full border border-gray-300">
              <thead className="bg-gray-100">
                <tr>
                  <th className="text-left px-3 py-2">Category</th>
                  <th className="text-left px-3 py-2">Product/Service</th>
                  <th className="text-left px-3 py-2">Description</th>
                  <th className="text-left px-3 py-2">Qty</th>
                  <th className="text-left px-3 py-2">UOM</th>
                  <th className="text-left px-3 py-2">Rate</th>
                  <th className="text-left px-3 py-2">Amount</th>
                </tr>
              </thead>
              <tbody>
                {invoiceData?.message?.data?.items.map((item, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-3 py-2">{item.category_name}</td>
                    <td className="px-3 py-2">{item.product_services}</td>
                    <td className="px-3 py-2">{item.product_description}</td>
                    <td className="px-3 py-2">{item.quantity}</td>
                    <td className="px-3 py-2">{item.uom}</td>
                    <td className="px-3 py-2">{item.rate}</td>
                    <td className="px-3 py-2">{item.amount}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Comments */}
          <div className="mt-4">
            <strong>Comments:</strong>
            <p className="text-sm text-gray-800 whitespace-pre-line mt-1">
              {invoiceData?.message?.data.comments}
            </p>
          </div>
          {invoiceData?.message?.data?.status === "Paid" &&
            invoiceData?.message?.data.payments[0]?.payment_type && (
              <div className="mt-4">
                <strong>Payment Type:</strong>
                <p className="text-sm text-gray-800 whitespace-pre-line mt-1">
                  {invoiceData?.message?.data.payments[0]?.payment_type || "-"}
                </p>
              </div>
            )}
          {invoiceData?.message?.data?.status === "Paid" &&
            invoiceData?.message?.data.payments[0]?.payment_comments && (
              <div className="mt-4">
                <strong>Payment Notes:</strong>
                <p className="text-sm text-gray-800 whitespace-pre-line mt-1">
                  {invoiceData?.message?.data.payments[0]?.payment_comments ||
                    "-"}
                </p>
              </div>
            )}
          <div className="mt-4">
            <strong>Invoice PDF</strong>
            <br />
            {invoiceData?.message?.data?.attachments?.filter(
              (file: any) => file.label === "Generated PDF"
            )?.length > 0 ? (
              invoiceData.message.data.attachments
                .filter((file: any) => file.label === "Generated PDF")
                .map((file: any, index: number) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Image className="w-5 h-5 text-gray-600" />
                    <a
                      href={`${baseUrl}${file.file_url}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {file.file_name}
                    </a>
                  </div>
                ))
            ) : (
              <p className="text-sm text-gray-500">No attachments</p>
            )}
          </div>

          <div className="space-y-2 mt-4">
            <strong className="block text-sm font-medium text-gray-700">
              Attached Files
            </strong>
            {invoiceData?.message?.data?.attachments?.filter(
              (file: any) => file.label === "Uploaded Attachment"
            )?.length > 0 ? (
              invoiceData.message.data.attachments
                .filter((file: any) => file.label === "Uploaded Attachment")
                .map((file: any, index: number) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Image className="w-5 h-5 text-gray-600" />
                    <span
                      onClick={() => handleFileClick(file)}
                      className="text-blue-600 hover:underline cursor-pointer"
                    >
                      {file.file_name}
                    </span>
                  </div>
                ))
            ) : (
              <p className="text-sm text-gray-500">No attachments</p>
            )}
          </div>
        </Card>

        {/* Customer Details */}
        <Card className="p-8 space-y-3">
          <h3 className="text-lg font-medium text-orange-600 tracking-wide mb-1">
            Customer Details
          </h3>
          <hr className="border-gray-300 mt-0" />
          <div className="text-sm space-y-2">
            <div>
              <strong>Customer in QuickBooks:</strong>{" "}
              {invoiceData?.message?.data?.quickbooks_customer_name}
            </div>
            <div>
              <strong>Bill To:</strong>{" "}
              <pre className="whitespace-pre-wrap">
                {invoiceData?.message?.data?.bill_to}
              </pre>
            </div>
            <div>
              <strong>Email:</strong> {invoiceData?.message?.data?.email}
            </div>
            <div>
              <strong>Contact:</strong> {invoiceData?.message?.data?.contact}
            </div>
          </div>

          {/* Totals */}
          <div className="space-y-1 w-full max-w-sm text-sm mt-4">
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">Sub Total:</span>
              <span className="text-black font-semibold">
                {invoiceData?.message?.data?.sub_total ?? "0.00"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">
                Tax <span className="text-gray-500 font-semibold"></span>:
              </span>
              <span className="text-black font-semibold">
                {invoiceData?.message?.data?.tax ?? "0.00"}
              </span>
            </div>
            <hr className="border-gray-300 my-3" />
            <div className="flex justify-between">
              <span className="text-gray-900 font-semibold">Total Amount:</span>
              <span className="text-black font-semibold">
                {invoiceData?.message?.data?.total_amount ?? "0.00"}
              </span>
            </div>
          </div>
          {/* {!invoiceData?.message?.data?.invoice_pdf_link && ( */}
          {/* <Button
            type="button"
            className="w-full"
            onClick={() =>
              handleGeneratePDF(invoiceData?.message?.data?.invoice_id)
            }
            disabled={isGeneratingPDF}
          >
            <FileText size={20} className="mr-2" />
            {isGeneratingPDF ? "Generating..." : "Generate Invoice PDF"}
          </Button> */}
          {/* )} */}
          {invoiceData?.message?.data?.carrier_booking_number &&
            invoiceData?.message?.data?.docket_id && (
              <Button
                type="button"
                className="w-full"
                onClick={() =>
                  navigate(
                    `/dashboard/customers/customer-docket-view/${invoiceData?.message?.data?.docket_id}?carrierBooking=${invoiceData?.message?.data?.carrier_booking_number}`
                  )
                }
              >
                <Eye size={20} className="mr-2" />
                {"Back To Docket"}
              </Button>
            )}
          <Button
            type="button"
            className="w-full"
            onClick={() =>
              (window.location.href = `/dashboard/customers/update-invoice/${id}?docketId=${invoiceData?.message?.data?.docket_id}`)
            }
            disabled={invoiceData?.message?.data?.status === "Voided"}
          >
            <Pen size={20} className="mr-2" />
            {"Edit Invoice"}
          </Button>

          {invoiceData?.message?.data?.status &&
            invoiceData?.message?.data?.status !== "Paid" &&
            invoiceData?.message?.data?.status !== "Voided" && (
              <Button
                type="button"
                className="w-full"
                onClick={() => {
                  setSelectedInvoiceId(
                    invoiceData?.message?.data?.quickbooks_invoice_id as string
                  ); // Save selected invoice
                  setSelectedPaymentType("Cash"); // Reset default
                  setIsDialogOpen(true); // Open dialog
                }}
                disabled={isChangeInvoiceStatus}
              >
               {isChangeInvoiceStatus ? "Loading..." : "Mark Invoice as Paid"}
              </Button>
            )}
          {invoiceData?.message?.data?.status === "Paid" && (
            <Button
              type="button"
              className="px-4 py-1 rounded-full bg-green-700 text-white text-sm font-semibold border border-green-700 cursor-default"
              disabled
            >
              Paid
            </Button>
          )}
          <Button
          variant="outline"
          className="ml-2"
          onClick={() => navigate(`/dashboard/customers/copy-invoice/${invoiceData?.message?.data?.name || invoiceId}`)}
        >
          Copy Invoice
        </Button>
        </Card>

        <ViewAttachedFiles
          open={previewOpen}
          selectedFile={selectedFile}
          onOpenChange={setPreviewOpen}
          setSelectedFile={setSelectedFile}
        />

        <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Bill Payment</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to mark this bill as paid?
              </AlertDialogDescription>
            </AlertDialogHeader>

            <fieldset className="my-4">
              <legend className="mb-2 font-medium">Payment Type</legend>
              <div className="space-y-2">
                {["Check", "Cash", "CreditCard"].map((type) => (
                  <label key={type} className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="paymentType"
                      value={type}
                      checked={selectedPaymentType === type}
                      onChange={() => setSelectedPaymentType(type)}
                    />
                    {type === "CreditCard" ? "Credit Card" : type}
                  </label>
                ))}
              </div>
            </fieldset>
            <fieldset className="my-4">
              <legend className="mb-2 font-medium">Notes:</legend>
              <div>
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2"
                  placeholder="Enter notes"
                  name="notes"
                  onChange={(e) => setNotes(e.target.value)}
                ></textarea>
              </div>
            </fieldset>

            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (selectedInvoiceId) {
                    handleChangeStatusInvoice(
                      selectedInvoiceId,
                      selectedPaymentType
                    );
                    setIsDialogOpen(false);
                  }
                }}
              >
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default ViewInvoicePage;
