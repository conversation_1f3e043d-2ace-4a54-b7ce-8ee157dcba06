// copyInvoice/index.tsx
import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Trash2, Minus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { useFieldArray, useForm, Controller } from "react-hook-form";
import { PlusCircle, Image, Save, ChevronsUpDown, Check, X } from "lucide-react";
import { fetchBookingLocations } from "@/services/admin/booking";
import { useQuery, useQueries } from "@tanstack/react-query";
import {
  getInvoiceDetails,
  getQuickbooksCustomers,
  getQuickbooksItems,
  createQuickbooksInvoice,
  getAllCustomers,
  getQuickbooksCategories,
  getQbDocketDetails,
} from "@/services/admin/invoiceGenerate";
import { ComboBoxPair } from "@/components/ui/comboBox-pair";
import { toast } from "sonner";
import { useNavigate, useParams, useSearchParams } from "react-router";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

export default function CopyInvoicePage() {
  const navigate = useNavigate();
  const { invoiceId } = useParams(); // route: /copy-invoice/:invoiceId
  const [searchParams] = useSearchParams();
  const docketId = searchParams.get("docketId"); // optional, same as generate page

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm({
    defaultValues: {
      purchase_order_number: "",
      customer_name: "",
      invoiceDate: "",
      bookingNo: "",
      hsCode: "",
      orgin_port: "",
      destination_port: "",
      bolNo: "",
      shippingDate: "",
      dueDate: "",
      incoterm: "",
      comments: "",
      attachment: undefined,
      quickbooks_customer_id: "",
      address: "",
      email_address: "",
      contact_number: "",
      items: [
        {
          category_id: "",
          category_name: "",
          product_services: "",
          product_description: "",
          qty: "1.00",
          uom: "",
          unit_price: "0.00",
          amount: 0,
          item_ref_id: "",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "items",
  });

  // Attachments local state (new files user uploads)
  const [attachments, setAttachments] = useState<File[]>([
    undefined as unknown as File,
  ]);

  // For product/category search (per-row)
  const [itemCategorySearch, setItemCategorySearch] = useState<{ [idx: number]: string }>({});
  const [productSearch, setProductSearch] = useState<{ [idx: number]: string }>({});
  const categorySearches = fields.map((_, idx) => itemCategorySearch[idx] || "");
  const productSearches = fields.map((_, idx) => productSearch[idx] || "");
  const categoryIds = fields.map((_, idx) => watch(`items.${idx}.category_id`));

  // Queries
  const { data: quickbooksCustomersData } = useQuery({
    queryKey: ["getQuickbooksCustomers"],
    queryFn: getQuickbooksCustomers,
  });

  const { data: customerDetails } = useQuery({
    queryKey: ["getAllCustomers"],
    queryFn: getAllCustomers,
  });

  const qbDocketQuery = useQuery({
    queryKey: ["getQbDocketDetails", { docketId }],
    queryFn: () => getQbDocketDetails(docketId as string),
    enabled: !!docketId,
  });

  // Per-row queries using useQueries (mirrors generateInvoice approach)
  const categoryQueries = useQueries({
    queries: categorySearches.map((search) => ({
      queryKey: ["getQuickbooksCategories", search],
      queryFn: () => getQuickbooksCategories(search),
      enabled: true,
    })),
  });

  const productQueries = useQueries({
    queries: fields.map((_, idx) => ({
      queryKey: ["getQuickbooksItems", productSearches[idx], categoryIds[idx]],
      queryFn: () => getQuickbooksItems(productSearches[idx], categoryIds[idx]),
      enabled: true,
    })),
  });

  // Invoice details query (to prefill)
  const { data: InvoiceDetails } = useQuery({
    queryKey: ["getInvoiceDetails", { invoiceId }],
    queryFn: () => getInvoiceDetails(invoiceId as string),
    enabled: !!invoiceId,
  });

  // ComboBox options
  const customerOptions =
    quickbooksCustomersData?.message?.customers?.map((c: any) => ({ label: c.display_name, value: c.id })) || [];

  const docketCustomerOptions =
    customerDetails?.message?.data?.map((c: any) => ({ label: c.customer_name, value: c.id })) || [];

  // set displayed quickbook customer label
  const selectedCustomerId = watch("quickbooks_customer_id");
  const [quickbookCustomerLabel, setQuickbookCustomerLabel] = useState("");

  useEffect(() => {
    if (!selectedCustomerId || !quickbooksCustomersData) return;
    const selected = quickbooksCustomersData?.message?.customers?.find((c: any) => c.id === selectedCustomerId);
    if (selected?.email) setValue("email_address", selected.email);
    else setValue("email_address", " ");
    if (selected?.billing_address) {
      const addr = selected.billing_address;
      const fullAddress = `${addr.Line1 || ""}, ${addr.City || ""}, ${addr.CountrySubDivisionCode || ""} ${addr.PostalCode || ""}`;
      setValue("address", fullAddress.trim());
    } else setValue("address", " ");
    if (selected?.phone) setValue("contact_number", selected.phone);
    else setValue("contact_number", " ");
    if (selected?.display_name) setQuickbookCustomerLabel(selected.display_name);
  }, [selectedCustomerId, quickbooksCustomersData, setValue]);

  // Attachments handlers
  const handleAttachmentChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setAttachments((prev) => {
      const updated = [...prev];
      updated[index] = file;
      return updated;
    });
  };

  const handleAddAttachment = () => setAttachments((prev) => [...prev, undefined as unknown as File]);
  const handleRemoveAttachment = (index: number) => setAttachments((prev) => prev.filter((_, i) => i !== index));

  // format helpers (same as your files)
  const formatDateToInput = (dateStr: string) => {
    if (!dateStr) return "";
    // handle both "MM-DD-YYYY" and ISO "YYYY-MM-DD"
    if (/\d{2}-\d{2}-\d{4}/.test(dateStr)) {
      const [month, day, year] = dateStr.split("-");
      return `${year}-${month}-${day}`;
    }
    const d = new Date(dateStr);
    if (!isNaN(d.getTime())) return d.toISOString().split("T")[0];
    return "";
  };

  // Prefill data from existing invoice but clear invoice_number and invoice/due dates
  useEffect(() => {
    if (!InvoiceDetails?.message) return;
    const invoice = InvoiceDetails.message.data;

    // Build items mapping as used in updateInvoice
    const itemsMapped =
      invoice.items?.map((item: any) => ({
        category_id: item.category_id || "",
        category_name: item.category_name || "",
        product_services: item.product_services || "",
        product_description: item.product_description || "",
        qty: item.quantity || 0,
        unit_price: item.rate || 0,
        amount: item.amount || 0,
        uom: item.uom || "",
        item_ref_id: item.item_id || "",
      })) || [];

    // Reset form with invoice values but clear fields that should be created fresh
    reset({
      customer_name: invoice.customer_id || "",
      quickbooks_customer_id: invoice.quickbooks_customer_id || "",
      // Clear dates so user chooses new ones
      invoiceDate: "",
      dueDate: "",
      // other fields copied
      bookingNo: invoice.booking_id || "",
      hsCode: invoice.hs_code || "",
      orgin_port: invoice.origin_port || "",
      destination_port: invoice.destination_port || "",
      bolNo: invoice.bol || "",
      shippingDate: invoice.shipping_date ? formatDateToInput(invoice.shipping_date) : "",
      incoterm: invoice.incoterm || "",
      address: invoice.bill_to || "",
      email_address: invoice.email || "",
      contact_number: invoice.contact || "",
      comments: invoice.comments || "",
      purchase_order_number: invoice.purchase_order_number || "",
      items: itemsMapped.length ? itemsMapped : [
        {
          category_id: "",
          category_name: "",
          product_services: "",
          product_description: "",
          qty: "1.00",
          uom: "",
          unit_price: "0.00",
          amount: 0,
          item_ref_id: "",
        },
      ],
    });

    // Save display label for quickbook customer if present
    setQuickbookCustomerLabel(invoice.quickbooks_customer_name || "");
    // We intentionally do NOT copy attachments into the new-upload state — user can add fresh attachments.

  }, [InvoiceDetails, reset]);

  // form submit
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formPayload, setFormPayload] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const onSubmit = (data: any) => {
    setFormPayload(data);
    setShowConfirmDialog(true);
  };

  const handleConfirmSubmit = async () => {
    setIsLoading(true);
    if (!formPayload) return;
    const data = formPayload;

    let itemsDetails: any[] = [];
    const hasInvalidItem =
      data.items.length > 1 && data.items.some((item: any) => !item.item_ref_id);

    if (hasInvalidItem) {
      toast.error("Each item must have a selected product/service.");
      setShowConfirmDialog(false);
      setIsLoading(false);
      return false;
    }

    const isOnlyEmptyItem = data.items.length === 1 && !data.items[0].item_ref_id;
    if (isOnlyEmptyItem) {
      toast.error("Please add at least one item to the invoice.");
      setShowConfirmDialog(false);
      setIsLoading(false);
      return false;
    } else {
      itemsDetails = data.items
        .filter((item: any) => item.product_services && item.product_services.trim() !== "")
        .map((item: any) => {
          const amount = Number(item.qty) * Number(item.unit_price);
          return {
            category_id: item.category_id || "",
            category_name: item.category_name || "",
            product_services: item.product_services,
            product_description: item.product_description,
            quantity: Number(item.qty),
            rate: Number(item.unit_price),
            amount,
            item_ref_id: item.item_ref_id,
            uom: item.uom || "",
          };
        });
    }

    if (itemsDetails.length === 0) {
      toast.error("Please add at least one item to the invoice.");
      setShowConfirmDialog(false);
      setIsLoading(false);
      return;
    }

    const payload = {
      customer_id: data.customer_name,
      quickbooks_customer_id: data.quickbooks_customer_id,
      invoice_data: {
        docket_id: docketId || "",
        customer_name: "", // we display label separately; backend mainly needs ids
        quickbooks_customer_name: quickbookCustomerLabel,
        email_address: data.email_address,
        contact_number: data.contact_number,
        address: data.address,
        due_date: data.dueDate,
        invoice_date: data.invoiceDate,
        tax: data.tax || 0,
        sub_total: data.sub_total || 0,
        total_amount: data.total_amount || 0,
        invoice_number: "", // must be blank for new invoice
        comments: data.comments,
        bill_to: data.address || "",
        shipping_date: data.shippingDate,
        bol: data.bolNo,
        hs_code: data.hsCode,
        orgin_port: data.orgin_port || "",
        destination_port: data.destination_port || "",
        booking_id: data.bookingNo || "",
        incoterm: data.incoterm || "",
        purchase_order_number: data.purchase_order_number || "",
      },
      items: itemsDetails,
    };

    const fileDetails = attachments.filter((f) => f instanceof File);

    try {
      const response = await createQuickbooksInvoice(payload, fileDetails);
      if (response?.message?.status_code === 200) {
        toast.success("Invoice created successfully (copy)!");
        setShowConfirmDialog(false);
        setFormPayload(null);
        // navigate to new invoice view (same as generate page)
        navigate("/dashboard/customers/view-invoice/" + response?.message?.invoice_name);
      } else {
        toast.error(response?.message?.Message || "Failed to create invoice");
        setShowConfirmDialog(false);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Failed to create invoice (copy):", error);
      toast.error("Failed to generate invoice.");
      setShowConfirmDialog(false);
      setIsLoading(false);
    }
    setIsLoading(false);
  };

  // For per-row category/product data inside the render loop
  const productQueriesData = productQueries;
  const categoryQueriesData = categoryQueries;

  // UI starts here — mostly copy of your generateInvoice markup but calls create endpoint
  return (
    <div className="p-1 space-y-6">
      <form onSubmit={handleSubmit(onSubmit)} className="p-1 grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="md:col-span-3 space-y-3 p-8">
          <h3 className="text-lg font-medium text-orange-600 tracking-wide mb-1">Copy Invoice (Edit & Create New)</h3>
          <hr className="border-gray-300 mt-0" />

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Customer Name</label>
            <ComboBoxPair
              label=""
              value={watch("customer_name")}
              onChange={(val) => setValue("customer_name", val)}
              options={docketCustomerOptions}
              placeholder="Select Customer"
            />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="purchaseOrder" className="block text-sm font-medium text-gray-700 mb-2">Purchase Order #</label>
              <Input className="border border-gray-300 text-sm px-3" id="purchaseOrder" {...register("purchase_order_number")} />
            </div>

            <div>
              <label htmlFor="invoiceDate" className="block text-sm font-medium text-gray-700 mb-2">Invoice Date</label>
              <Input className="border border-gray-300 text-sm px-3" id="invoiceDate" {...register("invoiceDate")} type="date" />
            </div>

            <div>
              <label htmlFor="bookingNo" className="block text-sm font-medium text-gray-700 mb-2">Booking #</label>
              <Input className="border border-gray-300 text-sm px-3" id="bookingNo" {...register("bookingNo")} />
            </div>

            <div>
              <label htmlFor="hsCode" className="block text-sm font-medium text-gray-700 mb-2">HS Code</label>
              <Input className="border border-gray-300 text-sm px-3" id="hsCode" {...register("hsCode")} />
            </div>

            <div>
              <label htmlFor="orgin_port" className="block text-sm font-medium text-gray-700 mb-2">Origin Port</label>
              <Input className="border border-gray-300 text-sm px-3" id="orgin_port" {...register("orgin_port")} />
            </div>

            <div>
              <label htmlFor="destination_port" className="block text-sm font-medium text-gray-700 mb-2">Destination Port</label>
              <Input className="border border-gray-300 text-sm px-3" id="destination_port" {...register("destination_port")} />
            </div>

            <div>
              <label htmlFor="bolNo" className="block text-sm font-medium text-gray-700 mb-2">Bol#</label>
              <Input className="border border-gray-300 text-sm px-3" id="bolNo" {...register("bolNo")} />
            </div>

            <div>
              <label htmlFor="shippingDate" className="block text-sm font-medium text-gray-700 mb-2">Shipping Date</label>
              <Input className="border border-gray-300 text-sm px-3" id="shippingDate" type="date" {...register("shippingDate")} />
            </div>

            <div>
              <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-2">Due Date<span className="text-red-500">*</span></label>
              <Input className="border border-gray-300 text-sm px-3" id="dueDate" type="date" {...register("dueDate")} required />
            </div>

            <div>
              <label htmlFor="incoterm" className="block text-sm font-medium text-gray-700 mb-2">Incoterm</label>
              <Controller
                control={control}
                name="incoterm"
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className="w-full border border-gray-300 text-sm px-3 h-10">
                      <SelectValue placeholder="Select Incoterm" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CNF">CNF</SelectItem>
                      <SelectItem value="FOB">FOB</SelectItem>
                      <SelectItem value="CIF">CIF</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>

          <h3 className="text-lg font-medium mt-4">Items</h3>
          <div className="w-full overflow-x-auto">
            <div className="min-w-[1200px]">
              <div className="grid grid-cols-[300px_250px_250px_80px_120px_100px_100px_40px] gap-3 text-sm font-medium text-muted-foreground px-1">
                <span className="text-black">Category</span>
                <span className="text-black">Product/Services</span>
                <span className="text-black">Product Description</span>
                <span className="text-black">Qty</span>
                <span className="text-black">UOM</span>
                <span className="text-black">Rate</span>
                <span className="text-black">Amount</span>
                <span></span>
              </div>

              {fields.map((field, index) => {
                const qty = watch(`items.${index}.qty`) || 0;
                const unit_price = watch(`items.${index}.unit_price`) || 0;
                const amount = (Number(qty) * Number(unit_price)).toFixed(2);

                const quickbooksItemsCategoryData = categoryQueriesData[index]?.data;
                const quickbooksItemsCategoryDataIsLoading = categoryQueriesData[index]?.isLoading;
                const quickbooksItemsData = productQueriesData[index]?.data;
                const quickbooksItemsDataIsLoading = productQueriesData[index]?.isLoading;

                const itemOptions =
                  quickbooksItemsData?.message?.items
                    ?.filter((it: any) => it.type !== "Category")
                    .map((it: any) => ({ label: it.name, value: it.name })) || [];

                const categoryOptions =
                  quickbooksItemsCategoryData?.message?.categories?.map((cat: any) => ({ label: cat.name, value: cat.id })) || [];

                return (
                  <React.Fragment key={field.id}>
                    <div className="grid grid-cols-[300px_250px_250px_80px_120px_100px_100px_40px] gap-3 px-1 items-center pt-2">
                      <div>
                        <Popover>
                          <PopoverTrigger asChild>
                            <button type="button" className="w-full max-h-[50px] border border-gray-300 rounded-sm px-3 py-2 flex justify-between items-start text-sm text-left whitespace-normal break-words overflow-y-auto">
                              <span className="flex-1 pr-2 overflow-y-auto max-h-[30px] leading-snug">
                                {categoryOptions.find((it: any) => it.value === watch(`items.${index}.category_id`))?.label || "Category"}
                              </span>
                              <ChevronsUpDown className="h-4 w-4 ml-2 opacity-50" />
                            </button>
                          </PopoverTrigger>
                          <PopoverContent className="w-[300px] p-0">
                            <Command>
                              <CommandInput placeholder="Search category" value={itemCategorySearch[index] || ""} onValueChange={(val) => setItemCategorySearch((prev) => ({ ...prev, [index]: val }))} className="h-9" />
                              <CommandList>
                                <CommandEmpty>
                                  {!itemCategorySearch[index] ? "Type to search..." : quickbooksItemsCategoryDataIsLoading ? "Loading..." : "No items found."}
                                </CommandEmpty>
                                <CommandGroup>
                                  {quickbooksItemsCategoryData?.message?.categories?.map((it: any) => (
                                    <CommandItem
                                      key={it.id}
                                      value={it.name}
                                      onSelect={() => {
                                        setValue(`items.${index}.category_id`, it.id);
                                        setValue(`items.${index}.category_name`, it.name);
                                      }}
                                    >
                                      {it.name}
                                      {watch(`items.${index}.category_id`) === it.id && <Check className="ml-auto h-4 w-4 text-primary" />}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div>
                        <Popover>
                          <PopoverTrigger asChild>
                            <button type="button" className="w-full max-h-[50px] border border-gray-300 rounded-sm px-3 py-2 flex justify-between items-start text-sm text-left whitespace-normal break-words overflow-y-auto">
                              <span className="flex-1 pr-2 overflow-y-auto max-h-[30px] leading-snug">{itemOptions.find((it: any) => it.value === watch(`items.${index}.product_services`))?.label || "Product/Service"}</span>
                              <ChevronsUpDown className="h-4 w-4 ml-2 opacity-50" />
                            </button>
                          </PopoverTrigger>
                          <PopoverContent className="w-[300px] p-0">
                            <Command>
                              <CommandInput placeholder="Search product/service..." value={productSearch[index] || ""} onValueChange={(val) => setProductSearch((prev) => ({ ...prev, [index]: val }))} className="h-9" />
                              <CommandList>
                                <CommandEmpty>
                                  {!productSearch[index] ? "Type to search..." : quickbooksItemsDataIsLoading ? "Loading..." : "No items found."}
                                </CommandEmpty>
                                <CommandGroup>
                                  {quickbooksItemsData?.message?.items
                                    ?.filter((it: any) => it.type !== "Category")
                                    .map((it: any) => (
                                      <CommandItem
                                        key={it.id}
                                        value={it.name}
                                        onSelect={() => {
                                          setValue(`items.${index}.product_services`, it.name);
                                          setValue(`items.${index}.product_description`, it.description || "");
                                          setValue(`items.${index}.unit_price`, it.unit_price || 0);
                                          setValue(`items.${index}.item_ref_id`, it.id || "");
                                        }}
                                      >
                                        {it.name}
                                        {watch(`items.${index}.product_services`) === it.name && <Check className="ml-auto h-4 w-4 text-primary" />}
                                      </CommandItem>
                                    ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div>
                        <Input {...register(`items.${index}.product_description`)} placeholder="Product Description" className="w-full" />
                      </div>

                      <div>
                        <Input {...register(`items.${index}.qty`, { min: { value: 0, message: "Qty must be 0 or greater" }, setValueAs: (v) => (v === "" ? 0 : parseFloat(v)) })} min={0} step="any" type="number" placeholder="0.00" className="w-full appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none" inputMode="decimal" />
                      </div>

                      <div>
                        <Controller control={control} name={`items.${index}.uom`} render={({ field }) => (
                          <Select value={field.value} onValueChange={field.onChange}>
                            <SelectTrigger className="h-10 w-full"><SelectValue placeholder="UOM" /></SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Kgs">Kgs</SelectItem>
                              <SelectItem value="MT">MT</SelectItem>
                              <SelectItem value="USt ton">USt ton</SelectItem>
                              <SelectItem value="Lbs">Lbs</SelectItem>
                              <SelectItem value="Container">Container</SelectItem>
                            </SelectContent>
                          </Select>
                        )} />
                      </div>

                      <div>
                        <Input {...register(`items.${index}.unit_price`, { validate: (val) => { const hasProduct = !!watch(`items.${index}.product_services`); if (!hasProduct) return true; return val && Number(val) !== 0 ? true : "Rate is required if product selected"; } })} type="number" step="any" placeholder="0.00" className="w-full appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none" inputMode="decimal" />
                      </div>

                      <div>
                        <Input value={amount} readOnly className="w-full" />
                      </div>

                      <div className="w-[40px]">
                        <Button type="button" onClick={() => remove(index)} variant="ghost" size="icon" className="text-muted-foreground">
                          <Trash2 className="w-6 h-6 text-black" />
                        </Button>
                      </div>
                    </div>

                    {watch(`items.${index}.item_ref_id`) && errors?.items?.[index]?.unit_price && (
                      <p className="text-red-500 text-xs mt-1">{errors.items[index].unit_price.message}</p>
                    )}
                  </React.Fragment>
                );
              })}

              <Button variant="ghost" className="text-orange-600 flex items-center gap-1 text-sm" type="button" onClick={() => append({ category_id: "", category_name: "", product_services: "", product_description: "", qty: "1.00", uom: "", unit_price: "0.00", amount: 0, item_ref_id: "" })}>
                <PlusCircle className="h-4 w-4" /> Add Item
              </Button>
            </div>
          </div>

          <div className="space-y-2 mt-4">
            <label htmlFor="comments" className="block text-sm font-medium text-gray-700">Comments</label>
            <Textarea id="comments" {...register("comments")} placeholder="Comments" />
          </div>

          <div className="space-y-2 mt-4">
            <label className="block text-sm font-medium text-gray-700">Attach File</label>

            {attachments.map((file, index) => (
              <div key={index} className="flex w-full items-center space-x-2">
                <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
                  <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                    <Image className="w-5 h-5 text-gray-600" />
                    <span className="font-medium">CHOOSE FILE</span>
                    <input type="file" className="hidden" onChange={(e) => handleAttachmentChange(e, index)} />
                  </label>

                  <div className="flex-1 flex items-center justify-between bg-gray-50 px-4 py-2 text-sm">
                    <span className="truncate text-gray-700">{file?.name || "Upload Attach File Here...."}</span>
                    {file && (
                      <button type="button" onClick={() => { const updated = [...attachments]; updated[index] = undefined as unknown as File; setAttachments(updated); }} className="text-gray-500 hover:text-red-600 ml-2" title="Remove file">
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>

                {attachments.length > 1 && (
                  <Button variant="outline" size="icon" onClick={() => setAttachments(attachments.filter((_, i) => i !== index))}>
                    <Minus />
                  </Button>
                )}

                {index === attachments.length - 1 && (
                  <Button variant="outline" size="icon" onClick={() => setAttachments([...attachments, undefined as unknown as File])}>
                    <PlusCircle />
                  </Button>
                )}
              </div>
            ))}

            {attachments.length === 0 && (
              <div className="flex w-full items-center space-x-2">
                <div className="flex flex-grow items-center border border-gray-300 rounded-md overflow-hidden">
                  <label className="flex items-center gap-2 px-3 py-3 bg-white border-r border-gray-300 text-sm cursor-pointer text-gray-700">
                    <Image className="w-5 h-5 text-gray-600" />
                    <span className="font-medium">CHOOSE FILE</span>
                    <input type="file" className="hidden" onChange={(e) => handleAttachmentChange(e, 0)} />
                  </label>
                  <input type="text" readOnly className="flex-1 px-4 py-2 text-sm bg-gray-50 text-gray-700 placeholder:text-gray-400 border-0 focus:outline-none" placeholder="Upload Attach File Here...." value="" />
                </div>
                <Button type="button" variant="outline" size="icon" className="shrink-0" onClick={handleAddAttachment}>
                  <PlusCircle className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>
        </Card>

        <Card className="p-8 space-y-3">
          <h3 className="text-lg font-medium text-orange-600 tracking-wide mb-1">Customer Details<span className="text-red-500">*</span></h3>
          <hr className="border-gray-300 mt-0" />

          <div>
            <ComboBoxPair label="Customer In QuickBooks" value={watch("quickbooks_customer_id")} onChange={(val) => setValue("quickbooks_customer_id", val)} options={customerOptions} placeholder="Select Customer" />
          </div>

          <div>
            <label className="text-sm text-gray-700 font-medium mb-2 block">Bill To</label>
            <Textarea {...register("address")} placeholder="Bill To" />
          </div>

          <div>
            <label className="text-sm text-gray-700 font-medium mb-2 block">Email Address</label>
            <Input {...register("email_address")} placeholder="Email Address" />
          </div>

          <div>
            <label className="text-sm text-gray-700 font-medium mb-2 block">Contact</label>
            <Input {...register("contact_number")} placeholder="Contact" />
          </div>

          <Button type="submit" className="w-full">
            <Save size={20} /> Create (Copy) Invoice
          </Button>
        </Card>

        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Create Invoice (from copy)</AlertDialogTitle>
              <AlertDialogDescription>Are you sure you want to create a new invoice from this copy?</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction className={isLoading ? "cursor-not-allowed" : ""} disabled={isLoading} onClick={(e) => { e.preventDefault(); handleConfirmSubmit(); }}>
                Yes, Create
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </form>
    </div>
  );
}
