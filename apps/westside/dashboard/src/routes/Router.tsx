import {
  createBrowserRouter,
  createRoutesFromElements,
  Route,
} from "react-router-dom";

// Layouts
import DashboardLayout from "@/layouts/customSidebar";
import RouteProtector from "@/components/auth/Protector";

// Pages
import SigninPage from "@/pages/admin/auth/signin";
import Dashboard from "@/pages";

import Bills from "@/pages/admin/jobs/bills";
import VendorsLists from "@/pages/admin/vendors/vendorsList";
import VendorProfile from "@/pages/admin/vendors/vendorView";

import CustomersListPage from "@/pages/admin/customers/list";
import CustomersViewPage from "@/pages/admin/customers/viewCustomer";
import DocketListPage from "@/pages/admin/customers/dockets/list";
import DocketViewPage from "@/pages/admin/customers/dockets/viewDocket";
import CreateDocket from "@/pages/admin/customers/dockets/create";

import JobListPage from "@/pages/vender/jobs/jobList";

import MyBookingPage from "@/pages/admin/booking/mybooking";
import CreateJobPage from "@/pages/admin/booking/mybooking/createJob";
import BookingConfirmationPage from "@/pages/admin/booking/bookingConfirmation";
import BookingRequestPage from "@/pages/admin/booking/bookingRequest";
import MyTemplatePage from "@/pages/admin/booking/myTemplate";
import ShippingInstructionRequestPage from "@/pages/admin/documentation/shippingInstruction/shippingInstructionRequest";
import ShippingInstructionConfirmationPage from "@/pages/admin/documentation/shippingInstruction/shippingInstructionConfirmation";

import MyShippingInstruction from "@/pages/admin/documentation/shippingInstruction/template";
import DocumentationPage from "@/pages/admin/documentation/DocumentationPage";
import CompanyTemplate from "@/pages/admin/documentation/companyTemplate";
import MyDraft from "@/pages/admin/documentation/myDraft";
import BillOfLaddingPage from "@/pages/admin/documentation/billOfLadding";
import EVGMPage from "@/pages/admin/documentation/eVGM";
import MyBillOfLaddingPage from "@/pages/admin/documentation/myBillOfLadding";

import OceanSchedulesPage from "@/pages/admin/plan/oceanSchedules";
import PublicRoute from "@/components/auth/PublicRoute";

import InvoiceListPage from "@/pages/admin/customers/invoice/index";
import GenerateInvoice from "@/pages/admin/customers/dockets/generateInvoice/index";

import JobView from "@/pages/vender/jobs/jobViews";
import TrackContainerPage from "@/pages/admin/track/list";
import AdminJobList from "@/pages/admin/jobs/jobs";
import AdminJobDetails from "@/pages/admin/vendors/job";
import AdminJobeditsPage from "@/pages/admin/jobs/jobsEdits";
import JobeditsPage from "@/pages/admin/vendors/job/jobEdits";

import ShipperEditPage from "@/pages/admin/shipper/editShipper/index";
import CoustomerDockets from "@/pages/customer/dockets/docketsList";
import CoustomerDocketsDetailsView from "@/pages/customer/dockets/docketCoustomerSideDetailsView";
import CoustomerInvoice from "@/pages/customer/invoice/invoiceList";
import CoustomerInvoiceDetails from "@/pages/customer/invoice/invoiceDetails";
import BillForm from "@/pages/admin/bill";
import BillView from "@/pages/admin/bill/view/index";
import ViewInvoice from "@/pages/admin/customers/dockets/viewInvoice/index";
import UpdateInvoice from "@/pages/admin/customers/invoice/updateInvoice/index";
import ReportPage from "@/pages/admin/reports/customers";
import VendorBillPage from "@/pages/vender/bills";
import UpdateBill from "@/pages/admin/bill/updateBill";
import CustomerProfile from "@/pages/customer/profile";
import VendorProfileView from "@/pages/vender/profile";
import CoustomerInvoiceUpdate from "@/pages/customer/invoice/invoiceUpdate";
import EVGMForm from "@/pages/admin/documentation/eVGM/create/index";
import BillReportPage from "@/pages/admin/reports/bill";
import VendorReportPage from "@/pages/admin/reports/vendor";
import InvoiceReportPage from "@/pages/admin/reports/invoice";
import ProductReportPage from "@/pages/admin/reports/product";
import CreateBillOfLaddingPage from "@/pages/admin/documentation/billOfLadding/createbillOfLading";
import EVGMViewPage from "@/pages/admin/documentation/eVGM/view";
import EVGMAmend from "@/pages/admin/documentation/eVGM/amend";
import CopyInvoicePage from "@/pages/admin/customers/dockets/copyInvoice";
import CopyInvoice from "@/views/admins/customers/dockets/copyInvoice";

const PageRoutes = createBrowserRouter(
  createRoutesFromElements(
    <>
      <Route
        path="signin"
        element={
          <PublicRoute>
            <SigninPage />
          </PublicRoute>
        }
      />

      {/* Protected Route Wrapper - All roles */}
      <Route element={<RouteProtector />}>
        <Route path="dashboard" element={<DashboardLayout />}>
          <Route index element={<Dashboard />} />

          <Route path="customer">
            <Route path="docketList" element={<CoustomerDockets />} />
            <Route path="invoiceList" element={<CoustomerInvoice />} />
            <Route
              path="dockets-Details-View/:id"
              element={<CoustomerDocketsDetailsView />}
            />
            <Route
              path="invoice-details-view/:id"
              element={<CoustomerInvoiceDetails />}
            />
            <Route
              path="update-invoice/:invoiceId"
              element={<CoustomerInvoiceUpdate />}
            />
          </Route>

          {/* Admin-only */}
          <Route
            element={<RouteProtector allowedRoles={["Vendor", "Admin"]} />}
          >
            <Route path="vendors">
              <Route path="jobs" element={<JobListPage />} />
              <Route path="job-view/:id" element={<JobView />} />
              <Route path="jobEdits/:id" element={<JobeditsPage />} />
              <Route path="bills" element={<Bills />} />
              <Route path="vendors-list" element={<VendorsLists />} />
              <Route path="vendor-view/:id" element={<VendorProfile />} />
            </Route>
            <Route path="customers">
              <Route path="list" element={<CustomersListPage />} />
              <Route path="customer-view/:id" element={<CustomersViewPage />} />
              <Route path="customer-docket-list" element={<DocketListPage />} />
              <Route
                path="customer-docket-view/:id"
                element={<DocketViewPage />}
              />
              <Route path="create-docket/:id" element={<CreateDocket />} />
              <Route
                path="customer-invoice-list"
                element={<InvoiceListPage />}
              />
              <Route path="generate-invoice" element={<GenerateInvoice />} />
              <Route path="view-invoice/:id" element={<ViewInvoice />} />
              <Route path="copy-invoice/:invoiceId" element={<CopyInvoice />} />
              <Route
                path="update-invoice/:invoiceId"
                element={<UpdateInvoice />}
              />
            </Route>
            <Route path="reports">
              <Route path="customer" element={<ReportPage />} />
              <Route path="vendor" element={<VendorReportPage />} />
              <Route path="invoice" element={<InvoiceReportPage />} />
              <Route path="bill" element={<BillReportPage />} />
              <Route path="product" element={<ProductReportPage />} />
            </Route>
            <Route path="track">
              <Route path="list" element={<TrackContainerPage />} />
            </Route>
            <Route path="bill-form" element={<BillForm />} />
            <Route path="bill-view/:id" element={<BillView />} />
            <Route path="update-bill/:billId" element={<UpdateBill />} />
          </Route>

          {/* Vendor-only */}
          <Route
            element={<RouteProtector allowedRoles={["Admin", "Vendor"]} />}
          >
            <Route path="jobs" element={<AdminJobList />} />
            <Route path="vendor/bills" element={<VendorBillPage />} />
            <Route path="admin-job-details/:id" element={<AdminJobDetails />} />
            <Route path="admin-job-edit/:id" element={<AdminJobeditsPage />} />
          </Route>

          {/* Customer & Admin & Vendor shared routes */}
          <Route
            element={
              <RouteProtector allowedRoles={["Customer", "Admin", "Vendor"]} />
            }
          >
            <Route path="booking">
              <Route path="my-booking">
                <Route index element={<MyBookingPage />} />
                <Route path="create-job/:id" element={<CreateJobPage />} />
                <Route
                  path="create-si"
                  element={<ShippingInstructionRequestPage />}
                >
                  <Route
                    path=":bookingId"
                    element={<ShippingInstructionRequestPage />}
                  />
                </Route>
              </Route>
              <Route
                path="booking-confirmation/:id"
                element={<BookingConfirmationPage />}
              />
              <Route path="booking-request" element={<BookingRequestPage />} />
              <Route path="template" element={<MyTemplatePage />} />
            </Route>

            <Route path="documentation">
              <Route
                path="my-shipping-instruction/shipping-confirmation/:id"
                element={<ShippingInstructionConfirmationPage />}
              />
              <Route element={<DocumentationPage />}>
                <Route
                  path="my-shipping-instruction"
                  element={<MyShippingInstruction />}
                />
                <Route path="company-template" element={<CompanyTemplate />} />
                <Route path="my-draft" element={<MyDraft />} />
              </Route>
              <Route path="bill-of-ladding" element={<BillOfLaddingPage />} />
              <Route path="eVGM-workspace" element={<EVGMPage />} />
              <Route
                path="my-bill-of-ladding/:id"
                element={<MyBillOfLaddingPage />}
              />
              <Route path="create-eVGM" element={<EVGMForm />} />
              <Route path="view-eVGM/:id" element={<EVGMViewPage />} />
              <Route
                path="create-bill-of-ladding"
                element={<CreateBillOfLaddingPage />}
              />
              <Route path="amend-eVGM/:id" element={<EVGMAmend />} />
            </Route>

            <Route
              path="plan/ocean-schedules"
              element={<OceanSchedulesPage />}
            />
            <Route path="profile" element={<ShipperEditPage />} />
            <Route path="customer-profile" element={<CustomerProfile />} />
            <Route path="vendor-profile" element={<VendorProfileView />} />
          </Route>
        </Route>
      </Route>
    </>
  )
);

export default PageRoutes;
