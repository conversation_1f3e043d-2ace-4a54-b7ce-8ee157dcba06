{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2025-04-07 14:27:43.102021", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["from_booking_confirmation_tab", "booking_request", "count", "equipment_name", "container_type_id", "code_value", "description", "comment", "inttra_booking_number", "carrier_booking_number", "volume_unit", "gross_volume", "bill_of_lading_id", "column_break_pcun", "job", "supplier_type", "service_type", "weight_value", "weight_type", "container_goods_image", "response_haulage_details", "for_si_tab", "si_id", "wood_declaration", "column_break_hqff", "carrier_seal_number", "shipper_seal_number", "section_break_vcxn", "cargo", "cargo_weight", "gross_weight", "is_active", "doc_version", "message_status", "ics2_summary_multiple_hbl_tab", "ics2_parties_section", "eori", "buyer", "seller", "house_bill_of_lading_section", "bill_of_lading_number", "place_of_acceptance_origin_of_goods", "place_of_final_delivery", "first_country", "countries_visited_in_between", "last_country", "method_of_payment", "column_break_ioqe", "ics2_party_multiple", "tab_4_tab", "evgm_section", "weight_determination_date_time", "tare_weight", "evgm_id", "evgm_xml_created", "column_break_cucv", "weight_determination_method", "verified_gross_mass", "verified_gross_mass_unit", "submitter_reference", "inttra_section", "message_version", "message_date_time_inttra", "state", "inttra_status", "inttra_evgmid", "xml_file_name_inttra", "column_break_pomn", "forwarders_reference_number", "inttra_error_code", "inttra_error_details", "shippers_reference_number", "customs_seal_number", "response_from_carrier_section", "carrier_status", "message_date_time_carrier", "xml_file_name_carrier", "column_break_qqao", "carrier_error_code", "carrier_error_details", "iload_app_tab", "max_weight", "cubic_capacity", "driver"], "fields": [{"fieldname": "count", "fieldtype": "Data", "in_list_view": 1, "label": "Count", "read_only": 1}, {"fieldname": "equipment_name", "fieldtype": "Data", "label": "Equipment Name"}, {"fetch_from": "container_type_id.typecode", "fieldname": "code_value", "fieldtype": "Data", "label": "Code Value"}, {"fetch_from": "container_type_id.shortdescription", "fieldname": "description", "fieldtype": "Text", "in_list_view": 1, "label": "Description"}, {"fieldname": "supplier_type", "fieldtype": "Data", "label": "Supplier Type"}, {"fieldname": "service_type", "fieldtype": "Data", "label": "Service Type"}, {"fieldname": "weight_value", "fieldtype": "Data", "label": "Weight Value"}, {"fieldname": "column_break_pcun", "fieldtype": "Column Break"}, {"fieldname": "weight_type", "fieldtype": "Data", "label": "Weight Type"}, {"fieldname": "container_goods_image", "fieldtype": "Table", "label": "Container goods image", "options": "Container goods image"}, {"fieldname": "booking_request", "fieldtype": "Link", "label": "Booking Request", "options": "Booking Request"}, {"fieldname": "from_booking_confirmation_tab", "fieldtype": "Tab Break", "label": "From Booking Confirmation"}, {"fieldname": "for_si_tab", "fieldtype": "Tab Break", "label": "For SI"}, {"fieldname": "cargo", "fieldtype": "Table", "label": "Cargo", "options": "Cargo"}, {"fieldname": "si_id", "fieldtype": "Link", "label": "SI ID", "options": "Shipping Instructions"}, {"fieldname": "column_break_hqff", "fieldtype": "Column Break"}, {"fieldname": "section_break_vcxn", "fieldtype": "Section Break"}, {"fieldname": "job", "fieldtype": "Link", "label": "Job", "options": "Job"}, {"fieldname": "shipper_seal_number", "fieldtype": "Data", "label": "Shipper Seal Number(s)"}, {"fieldname": "carrier_seal_number", "fieldtype": "Data", "label": "Carrier Seal Number(s)"}, {"fieldname": "tare_weight", "fieldtype": "Data", "label": "Tare weight"}, {"fieldname": "cargo_weight", "fieldtype": "Data", "label": "Cargo weight"}, {"fieldname": "gross_weight", "fieldtype": "Data", "label": "Gross weight"}, {"fieldname": "comment", "fieldtype": "Text", "label": "Comment"}, {"fieldname": "response_haulage_details", "fieldtype": "JSON", "label": "response Haulage Details"}, {"fieldname": "wood_declaration", "fieldtype": "Data", "label": "Wood Declaration"}, {"fieldname": "container_type_id", "fieldtype": "Link", "label": "Container Type Id", "options": "Container Type"}, {"fieldname": "inttra_booking_number", "fieldtype": "Data", "label": "Inttra Booking Number"}, {"fieldname": "carrier_booking_number", "fieldtype": "Data", "label": "Carrier Booking Number"}, {"fieldname": "gross_volume", "fieldtype": "Data", "label": "Gross Volume"}, {"fieldname": "volume_unit", "fieldtype": "Data", "label": "Volume Unit"}, {"fieldname": "bill_of_lading_id", "fieldtype": "Link", "label": "Bill of lading id", "options": "Bill of Lading"}, {"default": "1", "fieldname": "is_active", "fieldtype": "Check", "label": "Is Active"}, {"fieldname": "doc_version", "fieldtype": "Data", "label": "Doc Version"}, {"fieldname": "message_status", "fieldtype": "Data", "label": "Message Status"}, {"fieldname": "ics2_summary_multiple_hbl_tab", "fieldtype": "Tab Break", "label": "ICS2 Summary Multiple HBL"}, {"fieldname": "ics2_parties_section", "fieldtype": "Section Break", "label": "ICS2 Parties"}, {"fieldname": "eori", "fieldtype": "Data", "label": "EORI"}, {"fieldname": "buyer", "fieldtype": "Data", "label": "Buyer"}, {"fieldname": "seller", "fieldtype": "Data", "label": "<PERSON><PERSON>"}, {"fieldname": "house_bill_of_lading_section", "fieldtype": "Section Break", "label": "House Bill of Lading"}, {"fieldname": "bill_of_lading_number", "fieldtype": "Data", "label": "Bill of Lading Number"}, {"fieldname": "place_of_acceptance_origin_of_goods", "fieldtype": "Link", "label": "Place of Acceptance (Origin of Goods)", "options": "UNLOCODE Locations"}, {"fieldname": "place_of_final_delivery", "fieldtype": "Link", "label": "Place of Final Delivery", "options": "UNLOCODE Locations"}, {"fieldname": "first_country", "fieldtype": "Data", "label": "First Country"}, {"fieldname": "countries_visited_in_between", "fieldtype": "Data", "label": "Countries visited in between"}, {"fieldname": "last_country", "fieldtype": "Data", "label": "Last Country"}, {"fieldname": "method_of_payment", "fieldtype": "Select", "label": "Method of Payment", "options": "Account holder with carrier\nElectronic funds transfer\nNot pre-paid\nOther\nPayment in cash\nPayment by cheque\nPayment by credit card"}, {"fieldname": "column_break_ioqe", "fieldtype": "Column Break"}, {"fieldname": "ics2_party_multiple", "fieldtype": "Table", "label": "ICS2 Party Multiple", "options": "ICS2 Parties Involved"}, {"fieldname": "tab_4_tab", "fieldtype": "Tab Break", "label": "eVGM"}, {"fieldname": "evgm_section", "fieldtype": "Section Break", "label": "REQUEST"}, {"fieldname": "weight_determination_date_time", "fieldtype": "Datetime", "label": "Weight Determination Date Time"}, {"fieldname": "verified_gross_mass", "fieldtype": "Data", "label": "Verified Gross Mass"}, {"fieldname": "weight_determination_method", "fieldtype": "Select", "label": "Weight Determination Method", "options": "Method 1\nMethod 2"}, {"fieldname": "evgm_id", "fieldtype": "Link", "label": "eVGM Id", "options": "EVGM"}, {"fieldname": "column_break_cucv", "fieldtype": "Column Break"}, {"fieldname": "verified_gross_mass_unit", "fieldtype": "Data", "label": "Verified Gross Mass Unit"}, {"fieldname": "submitter_reference", "fieldtype": "Data", "label": "Submitter Reference"}, {"fieldname": "evgm_xml_created", "fieldtype": "Data", "label": "EVGM XML Created"}, {"fieldname": "inttra_section", "fieldtype": "Section Break", "label": "RESPONSE FROM INTTRA"}, {"fieldname": "message_version", "fieldtype": "Data", "label": "Message Version"}, {"fieldname": "state", "fieldtype": "Data", "label": "State"}, {"fieldname": "inttra_status", "fieldtype": "Data", "label": "Inttra Status"}, {"fieldname": "inttra_evgmid", "fieldtype": "Data", "label": "INTTRA eVGMID"}, {"fieldname": "forwarders_reference_number", "fieldtype": "Data", "label": "Forwarders Reference Number"}, {"fieldname": "column_break_pomn", "fieldtype": "Column Break"}, {"fieldname": "message_date_time_inttra", "fieldtype": "Datetime", "label": "Message Date Time"}, {"fieldname": "response_from_carrier_section", "fieldtype": "Section Break", "label": "RESPONSE FROM CARRIER"}, {"fieldname": "carrier_status", "fieldtype": "Data", "label": "Carrier Status"}, {"fieldname": "message_date_time_carrier", "fieldtype": "Datetime", "label": "Message Date Time"}, {"fieldname": "column_break_qqao", "fieldtype": "Column Break"}, {"fieldname": "xml_file_name_inttra", "fieldtype": "Data", "label": "XML File name Inttra"}, {"fieldname": "xml_file_name_carrier", "fieldtype": "Data", "label": "XML File Name Carrier"}, {"fieldname": "inttra_error_code", "fieldtype": "Data", "label": "Inttra Error Code"}, {"fieldname": "inttra_error_details", "fieldtype": "Small Text", "label": "Inttra Error Details"}, {"fieldname": "carrier_error_code", "fieldtype": "Data", "label": "Error Code"}, {"fieldname": "carrier_error_details", "fieldtype": "Small Text", "label": "<PERSON><PERSON><PERSON>"}, {"fieldname": "shippers_reference_number", "fieldtype": "Data", "label": "Shippers Reference Number"}, {"fieldname": "iload_app_tab", "fieldtype": "Tab Break", "label": "ILOAD App"}, {"fieldname": "max_weight", "fieldtype": "Data", "label": "Max Weight"}, {"fieldname": "cubic_capacity", "fieldtype": "Data", "label": "Cubic Capacity"}, {"fieldname": "driver", "fieldtype": "Data", "label": "Driver"}, {"fieldname": "customs_seal_number", "fieldtype": "Data", "label": "Customs Seal Number"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-12 11:17:37.399294", "modified_by": "Administrator", "module": "Westside", "name": "Equipments", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Guest", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}