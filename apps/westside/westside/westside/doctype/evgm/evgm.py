# Copyright (c) 2025, faircode and contributors
# For license information, please see license.txt

from datetime import datetime
import json
import random
import frappe
from frappe.model.document import Document
import os
from frappe.utils.jinja import render_template
import paramiko
import traceback


from westside.westside.doctype.bill_of_lading.bill_of_lading import convert_xml_to_json


class EVGM(Document):
	pass



@frappe.whitelist()
def featch_data_for_evgm(booking_number=None):
	try:
		if not booking_number:
			return {
				"status_code": 400,
				"message": "Booking number is required."
			}
		
		booking_name = frappe.get_value(
			"Booking Request",
			{"carrier_booking_number": booking_number},
			"name"
		)

		if not booking_name:
			return {
				"status_code": 404,
				"message": f"No booking request data found for this booking number."
			}

		booking_request = frappe.get_doc("Booking Request", booking_name)
		dct_booking_request = {}
		booking_request = booking_request.as_dict()
		dct_booking_request["booking_request_id"] = booking_request["name"]
		dct_booking_request["booking_number"] = booking_request["carrier_booking_number"]
		dct_booking_request["carrier"] = frappe.get_value("Carrier", booking_request["booking_agent"], ["name","partyalias","partyname1"],as_dict=True)
		dct_booking_request["shipper"] = frappe.get_value("Shipper", booking_request["shipper"], ["name","shipper_name","shipper_code","inttra_company_id"],as_dict=True)
		dct_booking_request["consignee"] = frappe.get_value("Customer DB", booking_request["consignee"], ["name","customer_name"],as_dict=True)

		doc_equipments = frappe.get_all(
			"Equipments",
			or_filters=[
				{"booking_request": booking_request["name"]},
				{"carrier_booking_number": booking_request["carrier_booking_number"]}
			],
			pluck="name"
		)

		lst_equipment = []
		for equipment in doc_equipments if doc_equipments else []:
			doc_name = frappe.db.exists("Equipments", {"name": equipment, "is_active": 1})
			if doc_name:
				full_doc = frappe.get_doc("Equipments", doc_name)
				dct_equipment = {
					"equipment_type_id": full_doc.container_type_id,
					"equipment_number": full_doc.equipment_name,
					"equipment_type": full_doc.code_value,
					"equiment_gross_weight": full_doc.cargo[0].get("cargo_gross_weight") if full_doc.cargo else  full_doc.cargo_weight or full_doc.weight_value,
					"equipment_seal_carrier": full_doc.carrier_seal_number,
					"equipment_seal_shipper": full_doc.shipper_seal_number,
					"equiment_gross_volume": full_doc.gross_volume,
					"equiment_tare_weight": full_doc.tare_weight,
					"verified_gross_mass": full_doc.verified_gross_mass,
					"verified_gross_mass_unit": full_doc.verified_gross_mass_unit,
					"customs_seal_number": full_doc.customs_seal_number,
					"weight_determination_method": full_doc.weight_determination_method,
					"weight_determination_date_time": full_doc.weight_determination_date_time
					
				}
				lst_equipment.append(dct_equipment)
		
		dct_booking_request["equipment_details"] = lst_equipment

		return {
			"status_code": 200,
			"message": "Data fetched successfully.",
			"data": dct_booking_request
		}

	
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Create EVGM Error")
		return {
			"status_code": 500,
			"message": str(e)
		}


@frappe.whitelist()
def create_evgm():
	try:
		dct_payload = json.loads(frappe.form_dict.get("data"))
		if not dct_payload:
			return {
				"status_code": 400,
				"message": "Payload is required."
			}
		if not dct_payload.get("booking_number"):
			return {
				"status_code": 400,
				"message": "Booking number is required."
			}
		if not frappe.db.exists("Shipping Instructions", {"carrier_booking_number":dct_payload.get("booking_number")}):
			return {
				"status_code": 400,
				"message": "Shipping instruction not found for this booking number."
			}
		
		lst_xml_data = []
		dct_insert_data = {
			"doctype": "EVGM",
			"booking_number" :  dct_payload.get("booking_number"),
			"acting_as": dct_payload.get("acting_as"),
			"carrier" : (dct_payload.get("carrier") or {}).get("name"),
			"responsible_party" : (dct_payload.get("responsible_party") or {}).get("name"),
			"authorized_party": (dct_payload.get("authorized_party") or {}).get("name"),
			"shipper" : (dct_payload.get("shipper") or {}).get("name"),
			# "forwarder" : (dct_payload.get("forwarder") or {}).get("name"),
			"terminal_operator" : (dct_payload.get("terminal_operator") or {}).get("name"),
			"approval_signature" : dct_payload.get("approval_signature"),
			"approval_datetime" : dct_payload.get("approval_datetime"),
			"partner_notification_emails": dct_payload.get("partner_notification_emails"),
			"notify_status_update" : dct_payload.get("notify_status_update"),
			"booking_request_id" : dct_payload.get("booking_request_id"),
			"message_guid": random.randint(10000000, 99999999),
			"message_date_time": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
			"message_version": "0.8",
			"state": "Original",
		}

		evgm = frappe.get_doc(dct_insert_data)
		evgm.insert(ignore_permissions=True)
		evgm_id = evgm.name

		#XML Datas
		shipper = frappe.db.get_value(
			"Shipper",
			(dct_payload.get("shipper") or {}).get("name"),
			["inttra_company_id", "shipper_name", "email", "phone"],
			as_dict=True
		)

		carrier = frappe.db.get_value(
			"Carrier",
			(dct_payload.get("carrier") or {}).get("name"),
			["partyname1", "partyalias"],
			as_dict=True
		)

		str_approval_signature = (dct_payload.get("approval_signature") or "").upper()

		dct_xml_data = {
			"submitter_id": shipper.inttra_company_id if shipper else "",
			"submitter_name": shipper.shipper_name if shipper else "",
			"submitter_email": shipper.email if shipper else "",
			"submitter_phone": shipper.phone if shipper else "",
			"shipper_reference": frappe.get_value("Equipments",{"booking_request": dct_payload.get("booking_request_id")},"si_id") or "",
			"verification_signature": str_approval_signature,
			"delegated": "false",
			"carrier_name": carrier.partyname1 if carrier else "",
			"carrier_sacc": carrier.partyalias if carrier else ""
		}

		dct_insert_data.pop("partner_notification_emails")
		dct_insert_data.pop("approval_datetime")
		str_approval_datetime = dct_payload.get("approval_datetime")
		verification_datetime = None
		if str_approval_datetime:
			try:
				dt_obj = datetime.fromisoformat(str_approval_datetime)
				verification_datetime = dt_obj.strftime("%Y-%m-%dT%H:%M:%S")
			except Exception as e:
				return {
					"status_code": 400,
					"message": "Invalid approval datetime format."
				}
		dct_xml_data["approval_datetime"] = verification_datetime
		
		emails = dct_payload.get("partner_notification_emails")
		if isinstance(emails, str):
			email_list = [e.strip() for e in emails.split(",") if e.strip()]
		else:
			email_list = list(emails)
		
		dct_xml_data["partner_notification_emails"] = email_list
				
		dct_xml_data.update(dct_insert_data)

		if evgm_id and dct_payload.get("equipment_details"):
			for equipment in dct_payload.get("equipment_details"):
				int_random = random.randint(10000, 99999)

				if not frappe.db.exists("Equipments", {"equipment_name":equipment.get("equipment_number"),"carrier_booking_number":dct_payload.get("booking_number")}):
					frappe.db.rollback()
					return {
						"status_code": 400,
						"message": f"{equipment.get('equipment_number')} container is does not exist for this booking number. Please check the container number or booking number and try again."
					}
				else:
					doc_equipment = frappe.get_doc("Equipments", {"equipment_name":equipment.get("equipment_number"),"carrier_booking_number":dct_payload.get("booking_number")})
				if doc_equipment.evgm_id:
					frappe.db.rollback()
					return {
						"status_code": 400,
						"message": f"{equipment.get('equipment_number')} container already has an EVGM."
					}
				if equipment.get("verified_gross_mass_unit") not in ["KGM","LBR"]:
					frappe.db.rollback()
					return {
						"status_code": 400,
						"message": "Invalid verified gross mass unit. Corerct values are KGM or LBR."
					}
				dct_equipment = {
					"evgm_id": evgm_id,
					"gross_volume": equipment.get("equiment_gross_weight"),
					"weight_value": equipment.get("equiment_gross_weight"),
					"tare_weight": equipment.get("equiment_tare_weight"),
					"weight_determination_date_time": equipment.get("weight_determination_date_time"),
					"weight_determination_method": equipment.get("weight_determination_method"),
					"verified_gross_mass": equipment.get("verified_gross_mass"),
					"verified_gross_mass_unit": equipment.get("verified_gross_mass_unit")  ,
					"submitter_reference": f"{equipment.get('equipment_number')}_{int_random}",
					"customs_seal_number": equipment.get("customs_seal_number"),
					"state": "Original",

				}
				doc_equipment.update(dct_equipment)
				doc_equipment.save(ignore_permissions=True)
				dct_eqp_xml = {}
				dct_eqp_xml["evgm_id"] = evgm_id
				dct_eqp_xml["container_number"] = doc_equipment.equipment_name
				dct_eqp_xml["verified_gross_mass"] = equipment.get("verified_gross_mass")
				dct_eqp_xml["verified_gross_mass_unit"] = equipment.get("verified_gross_mass_unit")  
				
				str_weight_determination_date_time = equipment.get("weight_determination_date_time")
				if str_weight_determination_date_time:
					try:
						dt_obj = datetime.fromisoformat(str_weight_determination_date_time)
						weight_determination_date_time = dt_obj.strftime("%Y-%m-%dT%H:%M:%S")
						dct_eqp_xml["weight_determination_date_time"] = weight_determination_date_time
					except Exception as e:
						return {
							"status_code": 400,
							"message": "Invalid weight determination datetime format."
						}
				
				dct_eqp_xml["weight_determination_method"] = equipment.get("weight_determination_method")
				dct_eqp_xml["submitter_reference"] = f"{equipment.get('equipment_number')}_{int_random}"
				dct_xml_entry = {**dct_xml_data, **dct_eqp_xml}
				lst_xml_data.append(dct_xml_entry)
		
		if lst_xml_data:
			# Generate XML
			xml_output = generate_evgm_xml(lst_xml_data)
			if xml_output.get("status_code") != 200:
				frappe.db.rollback()
				return {
					"status_code": 500,
					"message": xml_output.get("message")
				}
			else:
				frappe.db.commit()
				for file_items in xml_output.get("data"):
					for container, file_url in file_items.items():
						frappe.db.set_value("Equipments", {"equipment_name": container}, "evgm_xml_created", f"/files/{file_url}")
						frappe.db.commit()
				upload_resp = uplaod_evgm_xml_to_sftp(xml_output.get("data"))
				
				if upload_resp.get("status") != "success":
					return {
						"status_code": 500,
						"message": upload_resp.get("message")
					}
				
				return {
					"status_code": 200,
					"message": "EVGM created successfully."
				}

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Create EVGM Error")
		return {
			"status_code": 500,
			"message": str(e)
		}


@frappe.whitelist()	
def generate_evgm_xml(lst_data):

	try:
		lst_xml_output = []
		for item in lst_data:
			dct_xml_output = {}
			template_path = os.path.join(
			frappe.get_app_path("westside"), 
			"westside", "doctype", "evgm", "evgm_file.xml"
			)
			
			timestamp = datetime.now().strftime("%y%m%d%H%M%S")  # e.g., 250901134522
			file_name = f"evgm_{item.get('booking_number')}_{item.get('container_number')}_{timestamp}.xml"

			with open(template_path, "r", encoding="utf-8") as f:
				xml_template = f.read()

			rendered_xml = render_template(xml_template, item)

			non_blank_lines = [line for line in rendered_xml.splitlines() if line.strip()]
			cleaned_xml = "\n".join(non_blank_lines)

			with open(file_name, "w", encoding="utf-8") as f:
				f.write(cleaned_xml)

			file_doc = frappe.get_doc({
				"doctype": "File",
				"file_name": file_name,
				"is_private": 1,  
				"attached_to_doctype": "EVGM",  
				"attached_to_name": item.get("evgm_id"),
				"content": cleaned_xml
			}).insert(ignore_permissions=True)
			frappe.db.commit()
			
			dct_xml_output = {
				item.get("container_number"): file_name
			}

			lst_xml_output.append(dct_xml_output)
		
		return {
			"status_code": 200,
			"data": lst_xml_output
			}
			

		
	except Exception as e:
		frappe.db.rollback()
		frappe.log_error(frappe.get_traceback(), "Generate EVGM XML Error")
		return {
			"status_code": 500,
			"message": str(e)
		}

@frappe.whitelist()
def uplaod_evgm_xml_to_sftp(lst_xml_output):
	try:
		
		sftp_settings = frappe.get_doc("SFTP Server Settings")
		host = sftp_settings.host
		username = sftp_settings.username
		password = sftp_settings.get_password("password")
		port = sftp_settings.port or 22
		transport = paramiko.Transport((host, port))
		transport.connect(username=username, password=password)
		sftp = paramiko.SFTPClient.from_transport(transport)
		try:
			for file_dict in lst_xml_output:
				for key, file_name in file_dict.items():
					full_file_path = frappe.get_site_path("private","files", file_name)
					if not os.path.exists(file_name):
						frappe.throw(f"File not found: {file_name}")
					remote_path = f"inbound/vgm/{file_name}"
					sftp.put(full_file_path, remote_path)
		finally:
			sftp.close()
			transport.close()

		return {
			"status": "success",
			"message": "Files uploaded successfully."
		}

	except Exception as e:
		error_msg = f"❌ SFTP Upload Error: {str(e)}\n{traceback.format_exc()}"		
		frappe.log_error("SFTP Upload General Error", error_msg)
		return {"status": "error", "message": error_msg}
	


@frappe.whitelist()
def process_evgm_response_files(lst_vgm_files=None):
	try:
		if not lst_vgm_files:
			lst_vgm_files = frappe.form_dict.get("files")
		
			if not lst_vgm_files:
				return {
					"status": "error",
					"message": "No files provided."
				}

		lst_data = []

		for file_name in lst_vgm_files:
			try:
				file_path = frappe.utils.get_site_path("private", "sftp_downloads", file_name)
				if not os.path.exists(file_path):
					frappe.log_error(f"File {file_name} does not exist at {file_path}", "EVGM Response Missing File")
					continue

				json_output = convert_xml_to_json(file_path)			
				dct_data = json.loads(json_output or "{}")
				if not dct_data or not dct_data.get("RespondVGM"):
					continue

				respond_vgm = dct_data.get("RespondVGM")

				evgm_name = frappe.get_value("EVGM", {"booking_number": respond_vgm.get("BookingNumber")}, "name")
				if not evgm_name:
					continue

				
				temp_data = {
					"state": respond_vgm.get("State"),
					"message_version": respond_vgm.get("MessageVersion"),
					"forwarders_reference_number": respond_vgm.get("ForwardersReferenceNumber"),
					"shippers_reference_number": respond_vgm.get("ShippersReferenceNumber"),
					"inttra_evgmid": respond_vgm.get("INTTRAeVGMID"),
				}
				carrier_response = respond_vgm.get("Carrier") or {}
				if carrier_response:
					temp_data.update({
						"message_date_time_carrier": parse_datetime(respond_vgm.get("MessageDateTime")),
						"xml_file_name_carrier": file_name,
						"carrier_status": respond_vgm.get("Status"),
					})

					if respond_vgm.get("Status") == "Rejected":
						error = (respond_vgm.get("Errors") or {}).get("Error") or {}
						temp_data.update({
							"carrier_error_code": error.get("ErrorCode"),
							"carrier_error_details": error.get("ErrorDetails")
						}) if error else {}
				else:
					temp_data.update({
						"message_date_time_inttra": parse_datetime(respond_vgm.get("MessageDateTime")),
						"xml_file_name_inttra": file_name,
						"inttra_status": respond_vgm.get("Status")
					})
					if respond_vgm.get("Status") == "Rejected":
						error = (respond_vgm.get("Errors") or {}).get("Error") or {}
						temp_data.update({
							"inttra_error_code": error.get("ErrorCode"),
							"inttra_error_details": error.get("ErrorDetails")
						}) if error else {}

				equipment_name = respond_vgm.get("ContainerNumber")
				equipment_docname = frappe.get_value(
					"Equipments",
					{"evgm_id": evgm_name, "equipment_name": equipment_name},
					"name"
				)

				if equipment_docname:
					doc_equipment = frappe.get_doc("Equipments", equipment_docname)
					doc_equipment.update(temp_data)
					doc_equipment.save(ignore_permissions=True)
					frappe.db.commit()
					
					try:
						frappe.db.set_value("SFTP Downloaded Files", {"file_name": file_name}, "status", "Processed")
						frappe.db.commit()
					except:
						pass
				
				lst_data.append({"file": file_name, "status": "processed","data": dct_data})

			except Exception:
				frappe.log_error(frappe.get_traceback(), f"Process EVGM Response Error on {file_name}")
				continue

		return lst_data

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Process EVGM Response Error")
		return {
			"status_code": 500,
			"message": str(e)
		}




def parse_datetime(dt_str):
    if not dt_str:
        return None
    try:
        if dt_str.endswith("Z"):
            dt_str = dt_str[:-1]
            return datetime.fromisoformat(dt_str).strftime("%Y-%m-%d %H:%M:%S")
        
        return datetime.fromisoformat(dt_str).strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        return dt_str  



@frappe.whitelist()
def get_list_of_evgm(page=None, page_length=None):
	try:
		page = int(page or 1)
		page_length = int(page_length or 20)
		start = (page - 1) * page_length
		filters = {
			"is_active": 1,
			"evgm_id": ["!=", ""]
		}
		equipments = frappe.get_all(
			"Equipments",
			filters=filters,
			fields=["name", "equipment_name", "verified_gross_mass",
				"state",
				"inttra_status",
				"inttra_evgmid",
				"carrier_status",
				"evgm_id",
				"creation"
			],
			start=start,
			page_length=page_length,
			order_by="modified desc"
		)
		if not equipments:
			return {
				"status_code": 200,
				"data": [],
				"length": 0,
				"pagination": {
					"page": page,
					"page_length": page_length,
					"total_count": 0,
					"has_more": False
				}
			}
		
		evgm_ids = list({e.evgm_id for e in equipments if e.evgm_id})
		evgm_docs = frappe.get_all(
			"EVGM",
			filters={"name": ["in", evgm_ids]},
			fields=["name", "carrier","creation"]
		)
		evgm_carrier_map = {ev["name"]: ev["carrier"] for ev in evgm_docs}
		evgm_creation_map = {ev["name"]: ev["creation"] for ev in evgm_docs}

		carrier_ids = list({c for c in evgm_carrier_map.values() if c})
		carrier_docs = frappe.get_all(
			"Carrier",
			filters={"name": ["in", carrier_ids]},
			fields=["name", "party_short_name"]
		)
		carrier_map = {c["name"]: c["party_short_name"] for c in carrier_docs}

		lst_data = []
		for equipment in equipments:
			carrier = None
			evgm_carrier = evgm_carrier_map.get(equipment.evgm_id)
			if evgm_carrier:
				carrier = carrier_map.get(evgm_carrier)

			dct_equipment = {
				"evgm_id": equipment.evgm_id,
				"equipment_id": equipment.name,
				"equipment_name": equipment.equipment_name,
				"state": equipment.state or frappe.get_value("EVGM", equipment.evgm_id, "state"),
				"inttra_evgmid": equipment.inttra_evgmid,
				"verified_gross_mass": "{:.2f}".format(float(equipment.verified_gross_mass)) if equipment.verified_gross_mass else "0.00",
				"status": equipment.carrier_status or equipment.inttra_status or "Requested",
				"date_created": evgm_creation_map.get(equipment.evgm_id),
				"carrier": carrier,
			}
			lst_data.append(dct_equipment)

		total_count = frappe.db.count("Equipments", filters=filters)

		return {
			"status_code": 200,
			"data": lst_data,
			"pagination": {
				"page": page,
				"page_length": page_length,
				"total_count": total_count,
				"has_more": total_count > (page * page_length)
			}
		}
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Get EVGM List Error")
		return {
			"status_code": 500,
			"message": str(e)
		}
	

@frappe.whitelist()
def get_evgm_details(equipment_id=None):
	try:
		if not equipment_id:
			return {
				"status_code": 400,
				"message": "Equipment ID is required."
			}

		dct_data = {}
		doc_equipment = frappe.get_value(
			"Equipments",
			equipment_id,
			[
				"name", 
				"equipment_name",
				"state",
				"inttra_status",
				"inttra_evgmid",
				"carrier_status",
				"message_date_time_carrier",
				"message_date_time_inttra",
				"inttra_error_code",
				"inttra_error_details",
				"carrier_error_code",
				"carrier_error_details",
				"shippers_reference_number",
				"forwarders_reference_number",
				"shipper_seal_number",
				"carrier_seal_number", 
				"weight_determination_date_time",
				"weight_determination_method",
				"verified_gross_mass",
				"verified_gross_mass_unit",
				"evgm_id",
				"submitter_reference",
				"customs_seal_number",
				"container_type_id",
				"code_value",
				"weight_value",
				"tare_weight",
				"cargo_weight"
			],
			as_dict=True
		)

		if not doc_equipment:
			return {
				"status_code": 404,
				"message": "Equipment not found."
			}

		evgm_doc = None
		if doc_equipment.get("evgm_id"):
			evgm_doc = frappe.get_doc("EVGM", doc_equipment["evgm_id"])

		if not evgm_doc:
			return {
				"status_code": 404,
				"message": "EVGM not found for this equipment."
			}

		dct_data['equipment_id'] = doc_equipment["name"]
		dct_data['equipment_name'] = doc_equipment["equipment_name"]
		dct_data['state'] = doc_equipment["state"]
		dct_data['inttra_status'] = f"Inttra {doc_equipment['inttra_status']}" if doc_equipment.get("inttra_status") else None
		dct_data['carrier_status'] = f"Carrier {doc_equipment['carrier_status']}" if doc_equipment.get("carrier_status") else None
		dct_data['inttra_error_code'] = doc_equipment["inttra_error_code"]
		dct_data['inttra_error_details'] = doc_equipment["inttra_error_details"]
		dct_data['carrier_error_code'] = doc_equipment["carrier_error_code"]
		dct_data['carrier_error_details'] = doc_equipment["carrier_error_details"]
		dct_data['shipper_seal_number'] = doc_equipment["shipper_seal_number"]
		dct_data['carrier_seal_number'] = doc_equipment["carrier_seal_number"]
		dct_data['submitter_reference'] = doc_equipment["submitter_reference"]
		dct_data['shippers_reference_number'] = doc_equipment["shippers_reference_number"]
		dct_data['forwarders_reference_number'] = doc_equipment["forwarders_reference_number"]
		dct_data['weight_determination_method'] = doc_equipment["weight_determination_method"]
		dct_data['verified_gross_mass'] = "{:.2f}".format(float(doc_equipment.verified_gross_mass)) if doc_equipment.verified_gross_mass else "0.00"
		dct_data['verified_gross_mass_unit'] = doc_equipment["verified_gross_mass_unit"]
		dct_data['weight_determination_date_time'] = doc_equipment["weight_determination_date_time"]
		dct_data['message_date_time_inttra'] = doc_equipment["message_date_time_inttra"]
		dct_data['message_date_time_carrier'] = doc_equipment["message_date_time_carrier"]
		dct_data['inttra_evgmid'] = doc_equipment["inttra_evgmid"]
		dct_data['evgm_id'] = doc_equipment["evgm_id"]
		dct_data['acting_as'] = evgm_doc.acting_as
		dct_data['booking_number'] = evgm_doc.booking_number
		dct_data['approval_signature'] = evgm_doc.approval_signature
		dct_data['approval_datetime'] = evgm_doc.approval_datetime
		dct_data['partner_notification_emails'] = evgm_doc.partner_notification_emails
		dct_data['created'] = evgm_doc.creation.strftime("%m-%d-%Y %H:%M")
		dct_data['notify_status_update'] = evgm_doc.notify_status_update
		dct_data['equipment_details'] ={
			"equipment_number": doc_equipment["equipment_name"],
			"equipment_type_id": doc_equipment["container_type_id"],
			"equipment_type": doc_equipment["code_value"],
			"equiment_gross_weight": doc_equipment["weight_value"],
			"equiment_tare_weight": doc_equipment["tare_weight"],
			"verified_gross_mass": doc_equipment["verified_gross_mass"],
			"verified_gross_mass_unit": doc_equipment["verified_gross_mass_unit"],
			"customs_seal_number": doc_equipment["customs_seal_number"],
			"weight_determination_method": doc_equipment["weight_determination_method"],
			"weight_determination_date_time": doc_equipment["weight_determination_date_time"]
		}

		if evgm_doc.carrier:
			dct_data['evgm_carrier'] = frappe.get_value("Carrier", evgm_doc.carrier,["name", "partyalias","partyname1","party_short_name","address","inttra_id","postal_code","country_code"],as_dict=True)
		else:
			dct_data['evgm_carrier'] = None
		
		if evgm_doc.shipper:
			dct_data['evgm_shipper'] = frappe.get_value("Shipper", evgm_doc.shipper,["name", "shipper_name","shipper_code","inttra_company_id","custom_address","postal_code","country","email","phone"],as_dict=True)
		else:
			dct_data['evgm_shipper'] = None

		if evgm_doc.responsible_party and evgm_doc.responsible_party.startswith("SHI"):
			dct_data['evgm_responsible_party'] = frappe.get_value("Shipper", evgm_doc.responsible_party,["name", "shipper_name","shipper_code","inttra_company_id","custom_address","postal_code","country","email","phone"],as_dict=True)
		elif evgm_doc.responsible_party and evgm_doc.responsible_party.startswith("CUS"):
			dct_data['evgm_responsible_party'] = frappe.get_value("Customer DB", evgm_doc.responsible_party,["name", "customer_name","inttra_company_id","customer_address","customer_zip","customer_country","email_id","phone"],as_dict=True)
		else:
			dct_data['evgm_responsible_party'] = None

		if evgm_doc.authorized_party and evgm_doc.authorized_party.startswith("SHI"):
			dct_data['evgm_authorized_party'] = frappe.get_value("Shipper", evgm_doc.authorized_party,["name", "shipper_name","shipper_code","inttra_company_id","custom_address","postal_code","country","email","phone"],as_dict=True)
		elif evgm_doc.authorized_party and evgm_doc.authorized_party.startswith("CUS"):
			dct_data['evgm_authorized_party'] = frappe.get_value("Customer DB", evgm_doc.authorized_party,["name", "customer_name","inttra_company_id","customer_address","customer_zip","customer_country","email_id","phone"],as_dict=True)
		else:
			dct_data['evgm_authorized_party'] = None

		if evgm_doc.terminal_operator and evgm_doc.terminal_operator.startswith("SHI"):
			dct_data['evgm_terminal_operator'] = frappe.get_value("Shipper", evgm_doc.terminal_operator,["name", "shipper_name","shipper_code","inttra_company_id","custom_address","postal_code","country","email","phone"],as_dict=True)
		elif evgm_doc.terminal_operator and evgm_doc.terminal_operator.startswith("CUS"):
			dct_data['evgm_terminal_operator'] = frappe.get_value("Customer DB", evgm_doc.terminal_operator,["name", "customer_name","inttra_company_id","customer_address","customer_zip","customer_country","email_id","phone"],as_dict=True)
		else:
			dct_data['evgm_terminal_operator'] = None
		
		if evgm_doc.forwarder:
			dct_data['evgm_forwarder'] = frappe.get_value("Forwarder", evgm_doc.forwarder,[ "name","contact_name","inttra_company_id","contact_number","postal_code","country","email","address"],as_dict=True)
		else:
			dct_data['evgm_forwarder'] = None

		return {
			"status_code": 200,
			"data": dct_data
		}

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Get EVGM Details Error")
		return {
			"status_code": 500,
			"message": str(e)
		}




@frappe.whitelist()
def amend_evgm():
	try:
		dct_payload = json.loads(frappe.form_dict.get("data"))
		if not dct_payload:
			return {
				"status_code": 400,
				"message": "Payload is required."
			}
		
		lst_xml_data = []
		doc_evgm = frappe.get_doc("EVGM", dct_payload.get("evgm_id"))
		if not doc_evgm:
			return {
				"status_code": 400,
				"message": "EVGM not found."
			}
		dct_insert_data = {
			"booking_number" :  dct_payload.get("booking_number"),
			"acting_as": dct_payload.get("acting_as"),
			"carrier" : (dct_payload.get("carrier") or {}).get("name"),
			"responsible_party" : (dct_payload.get("responsible_party") or {}).get("name"),
			"authorized_party": (dct_payload.get("authorized_party") or {}).get("name"),
			"shipper" : (dct_payload.get("shipper") or {}).get("name"),
			# "forwarder" : (dct_payload.get("forwarder") or {}).get("name"),
			"terminal_operator" : (dct_payload.get("terminal_operator") or {}).get("name"),
			"approval_signature" : dct_payload.get("approval_signature"),
			"approval_datetime" : dct_payload.get("approval_datetime"),
			"partner_notification_emails": dct_payload.get("partner_notification_emails"),
			"notify_status_update" : dct_payload.get("notify_status_update"),
			"booking_request_id" : dct_payload.get("booking_request_id"),
			"message_date_time": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
			"message_version": "0.8"
		}

		doc_evgm.update(dct_insert_data)
		doc_evgm.save(ignore_permissions=True)
		evgm_id = doc_evgm.name

		#XML Datas
		shipper = frappe.db.get_value(
			"Shipper",
			(dct_payload.get("shipper") or {}).get("name"),
			["inttra_company_id", "shipper_name", "email", "phone"],
			as_dict=True
		)

		carrier = frappe.db.get_value(
			"Carrier",
			(dct_payload.get("carrier") or {}).get("name"),
			["partyname1", "partyalias"],
			as_dict=True
		)

		str_approval_signature = (dct_payload.get("approval_signature") or "").upper()

		dct_xml_data = {
			"submitter_id": shipper.inttra_company_id if shipper else "",
			"submitter_name": shipper.shipper_name if shipper else "",
			"submitter_email": shipper.email if shipper else "",
			"submitter_phone": shipper.phone if shipper else "",
			"shipper_reference": doc_evgm.shipper_reference,
			"verification_signature": str_approval_signature,
			"message_guid": doc_evgm.message_guid,
			"delegated": "false",
			"state": "Amended",
			"carrier_name": carrier.partyname1 if carrier else "",
			"carrier_sacc": carrier.partyalias if carrier else ""
		}

		dct_insert_data.pop("partner_notification_emails")
		dct_insert_data.pop("approval_datetime")
		str_approval_datetime = dct_payload.get("approval_datetime")
		verification_datetime = None
		if str_approval_datetime:
			try:
				dt_obj = datetime.fromisoformat(str_approval_datetime)
				verification_datetime = dt_obj.strftime("%Y-%m-%dT%H:%M:%S")
			except Exception as e:
				return {
					"status_code": 400,
					"message": "Invalid approval datetime format."
				}
		dct_xml_data["approval_datetime"] = verification_datetime
		
		emails = dct_payload.get("partner_notification_emails")
		if isinstance(emails, str):
			email_list = [e.strip() for e in emails.split(",") if e.strip()]
		else:
			email_list = list(emails)
		
		dct_xml_data["partner_notification_emails"] = email_list
				
		dct_xml_data.update(dct_insert_data)

		if evgm_id and dct_payload.get("equipment_details"):
			for equipment in dct_payload.get("equipment_details"):
				if not frappe.db.exists("Equipments", {"equipment_name":equipment.get("equipment_number"),"carrier_booking_number":dct_payload.get("booking_number")}):
					frappe.db.rollback()
					return {
						"status_code": 400,
						"message": f"{equipment.get('equipment_number')} container is does not exist for this booking number. Please check the container number or booking number and try again."
					}
				else:
					doc_equipment = frappe.get_doc("Equipments", {"equipment_name":equipment.get("equipment_number"),"carrier_booking_number":dct_payload.get("booking_number")})
				
				if equipment.get("verified_gross_mass_unit") not in ["KGM","LBR"]:
					frappe.db.rollback()
					return {
						"status_code": 400,
						"message": "Invalid verified gross mass unit. Corerct values are KGM or LBR."
					}
				dct_equipment = {
					"evgm_id": evgm_id,
					"gross_volume": equipment.get("equiment_gross_weight"),
					"weight_value": equipment.get("equiment_gross_weight"),
					"tare_weight": equipment.get("equiment_tare_weight"),
					"weight_determination_date_time": equipment.get("weight_determination_date_time"),
					"weight_determination_method": equipment.get("weight_determination_method"),
					"verified_gross_mass": equipment.get("verified_gross_mass"),
					"verified_gross_mass_unit": equipment.get("verified_gross_mass_unit")  ,
					"customs_seal_number": equipment.get("customs_seal_number"),
					"state": "Amended",
					"inttra_status": "Requested",
					"carrier_status": "Requested"
				}

				doc_equipment.update(dct_equipment)
				doc_equipment.save(ignore_permissions=True)

				dct_eqp_xml = {}
				dct_eqp_xml["evgm_id"] = evgm_id
				dct_eqp_xml["container_number"] = doc_equipment.equipment_name
				dct_eqp_xml["verified_gross_mass"] = equipment.get("verified_gross_mass")
				dct_eqp_xml["verified_gross_mass_unit"] = equipment.get("verified_gross_mass_unit")  
				dct_eqp_xml["inttra_evgmid"] = doc_equipment.get("inttra_evgmid")
				dct_eqp_xml["submitter_reference"] = doc_equipment.get("submitter_reference")
				
				str_weight_determination_date_time = equipment.get("weight_determination_date_time")
				if str_weight_determination_date_time:
					try:
						dt_obj = datetime.fromisoformat(str_weight_determination_date_time)
						weight_determination_date_time = dt_obj.strftime("%Y-%m-%dT%H:%M:%S")
						dct_eqp_xml["weight_determination_date_time"] = weight_determination_date_time
					except Exception as e:
						return {
							"status_code": 400,
							"message": "Invalid weight determination datetime format."
						}
				
				dct_eqp_xml["weight_determination_method"] = equipment.get("weight_determination_method")
				dct_xml_entry = {**dct_xml_data, **dct_eqp_xml}
				lst_xml_data.append(dct_xml_entry)
		
		if lst_xml_data:
			xml_output = generate_evgm_xml(lst_xml_data)
			if xml_output.get("status_code") != 200:
				frappe.db.rollback()
				return {
					"status_code": 500,
					"message": xml_output.get("message")
				}
			else:
				frappe.db.commit()
				for file_items in xml_output.get("data"):
					for container, file_url in file_items.items():
						frappe.db.set_value("Equipments", {"equipment_name": container}, "evgm_xml_created", f"/files/{file_url}")
						frappe.db.commit()
				upload_resp = uplaod_evgm_xml_to_sftp(xml_output.get("data"))
				
				if upload_resp.get("status") != "success":
					return {
						"status_code": 500,
						"message": upload_resp.get("message")
					}
				
				return {
					"status_code": 200,
					"message": "EVGM Amended successfully."
				}

	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Create EVGM Error")
		return {
			"status_code": 500,
			"message": str(e)
		}



@frappe.whitelist()
def cancel_evgm(equipment_id=None):
	try:
		if not equipment_id:
			return {
				"status_code": 400,
				"message": "Equipment ID is required."
			}

		doc_equipment = frappe.get_doc("Equipments", equipment_id)
		if not doc_equipment.evgm_id:
			return {
				"status_code": 404,
				"message": "No EVGM linked with this equipment."
			}

		doc_evgm = frappe.get_doc("EVGM", doc_equipment.evgm_id)

		dct_insert_data = {
			"booking_number": doc_evgm.booking_number,
			"acting_as": doc_evgm.acting_as,
			"carrier": doc_evgm.carrier,
			"responsible_party": doc_evgm.responsible_party,
			"authorized_party": doc_evgm.authorized_party,
			"shipper": doc_evgm.shipper,
			"terminal_operator": doc_evgm.terminal_operator,
			"approval_signature": doc_evgm.approval_signature,
			"partner_notification_emails": doc_evgm.partner_notification_emails,
			"notify_status_update": doc_evgm.notify_status_update,
			"booking_request_id": doc_evgm.booking_request_id,
			"message_date_time": datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
			"message_version": "0.8",
			"state": "Cancel",
		}

		str_approval_datetime = doc_evgm.approval_datetime
		if str_approval_datetime:
			try:
				verification_datetime = str_approval_datetime.strftime("%Y-%m-%dT%H:%M:%S")
			except Exception as e:
				return {
					"status_code": 400,
					"message": "Invalid approval datetime format."
				}
		dct_insert_data["approval_datetime"] = verification_datetime

		shipper = frappe.db.get_value(
			"Shipper", doc_evgm.shipper,
			["inttra_company_id", "shipper_name", "email", "phone"],
			as_dict=True
		) or {}

		carrier = frappe.db.get_value(
			"Carrier", doc_evgm.carrier,
			["partyname1", "partyalias"],
			as_dict=True
		) or {}

		dct_xml_data = {
			"submitter_id": shipper.get("inttra_company_id"),
			"submitter_name": shipper.get("shipper_name"),
			"submitter_email": shipper.get("email"),
			"submitter_phone": shipper.get("phone"),
			"shipper_reference": doc_evgm.shipper_reference,
			"verification_signature": doc_evgm.approval_signature.upper(),
			"message_guid": doc_evgm.message_guid,
			"delegated": "false",
			"carrier_name": carrier.get("partyname1"),
			"carrier_sacc": carrier.get("partyalias")
		}
		dct_xml_data.update(dct_insert_data)

		lst_xml_data = []
		if doc_equipment:
			str_weight_determination_date_time = doc_equipment.weight_determination_date_time
			weight_determination_date_time = ""
			if str_weight_determination_date_time:
				try:
					weight_determination_date_time = str_weight_determination_date_time.strftime("%Y-%m-%dT%H:%M:%S")
				except Exception as e:
					return {
						"status_code": 400,
						"message": "Invalid weight determination datetime format."
					}
			dct_eqp_xml = {
				"evgm_id": doc_equipment.evgm_id,
				"container_number": doc_equipment.equipment_name,
				"verified_gross_mass": doc_equipment.verified_gross_mass,
				"verified_gross_mass_unit": doc_equipment.verified_gross_mass_unit,
				"inttra_evgmid": doc_equipment.inttra_evgmid,
				"submitter_reference": doc_equipment.submitter_reference,
				"weight_determination_date_time": weight_determination_date_time,
				"weight_determination_method": doc_equipment.weight_determination_method
			}
			lst_xml_data.append({**dct_xml_data, **dct_eqp_xml})

		if not lst_xml_data:
			return {
				"status_code": 404,
				"message": "No equipments found for this EVGM."
			}

		xml_output = generate_evgm_xml(lst_xml_data)
		if xml_output.get("status_code") != 200:
			frappe.db.rollback()
			return {
				"status_code": 500,
				"message": xml_output.get("message")
			}

		for file_items in xml_output.get("data", []):
			for container, file_url in file_items.items():
				frappe.db.set_value(
					"Equipments",
					{"equipment_name": container},
					"evgm_xml_created",
					f"/files/{file_url}"
				)

		upload_resp = uplaod_evgm_xml_to_sftp(xml_output.get("data"))
		if upload_resp.get("status") != "success":
			frappe.db.rollback()
			return {
				"status_code": 500,
				"message": upload_resp.get("message")
			}
		
		frappe.db.set_value(
			"Equipments", 
			equipment_id, 
			{
				"carrier_status": "Requested",
				"state": "Cancel",
			}
		)
		frappe.db.commit()
		return {
			"status_code": 200,
			"message": "EVGM cancelled successfully."
		}

   	
	
	except Exception as e:
		frappe.db.rollback()
		frappe.log_error(frappe.get_traceback(), "Cancel EVGM Error")
		return {
			"status_code": 500,
			"message": str(e)
		}
