app_name = "westside"
app_title = "Westside"
app_publisher = "faircode"
app_description = "Westside"
app_email = "<EMAIL>"
app_license = "mit"

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
# add_to_apps_screen = [
# 	{
# 		"name": "westside",
# 		"logo": "/assets/westside/logo.png",
# 		"title": "Westside",
# 		"route": "/westside",
# 		"has_permission": "westside.api.permission.has_app_permission"
# 	}
# ]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/westside/css/westside.css"
# app_include_js = "/assets/westside/js/westside.js"

# include js, css files in header of web template
# web_include_css = "/assets/westside/css/westside.css"
# web_include_js = "/assets/westside/js/westside.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "westside/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
# doctype_js = {"doctype" : "public/js/doctype.js"}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "westside/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "westside.utils.jinja_methods",
# 	"filters": "westside.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "westside.install.before_install"
# after_install = "westside.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "westside.uninstall.before_uninstall"
# after_uninstall = "westside.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "westside.utils.before_app_install"
# after_app_install = "westside.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "westside.utils.before_app_uninstall"
# after_app_uninstall = "westside.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "westside.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
# 	}
# }

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"westside.api.ensure_sftp_watcher_running"
# 	],
	# "daily": [
	# 	"westside.tasks.daily"
	# ],
	# "hourly": [
	# 	"westside.tasks.hourly"
	# ],
	# "weekly": [
	# 	"westside.tasks.weekly"
	# ],
	# "monthly": [
	# 	"westside.tasks.monthly"
	# ],
# }

# Testing
# -------

# before_tests = "westside.install.before_tests"

# Overriding Methods
# ------------------------------
#
## In hooks.py

#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "westside.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["westside.utils.before_request"]
# after_request = ["westside.utils.after_request"]

# Job Events
# ----------
# before_job = ["westside.utils.before_job"]
# after_job = ["westside.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"westside.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }

doc_events = {
    "Customer DB": {
        "after_insert": "westside.westside.doctype.customer_db.customer_db.after_insert"
    },
     "Vendor": {
        "after_insert": "westside.westside.doctype.vendor.vendor.after_insert"
    }
}

api_methods = {
    "POST": [
        "westside.www.webhooks.receive_inttra_webhook"
    ]
}


scheduler_events = {
    "cron": {
        # "*/15 * * * *": [
        #     "westside.api.email_schedule.email_cron_job"
        # ],
        "*/10 * * * *": [ 
            "westside.api.sftp_file_dwnld.download_xml_from_sftp"
        ]
    }
}
# after_migrate = ["westside.api.sftp_file_dwnld.enqueue_sftp_watcher"]



# "0 8,12 * * *":   # cron is set for 8 AM and 12 PM sharp (0 8,12 * * *).

website_route_rules = [{'from_route': '/dashboard/<path:app_path>', 'to_route': 'dashboard'},]
