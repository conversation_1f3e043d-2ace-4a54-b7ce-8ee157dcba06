import frappe, random
from westside.www.Authentication.auth_decorators import role_required




@frappe.whitelist(allow_guest=True)
def driver_login_with_passkey(passkey):
    """
    Driver login using a passkey.
    Validates if passkey exists in <PERSON><PERSON><PERSON>'s child table 'Passkey'.
    """

    if not passkey:
        return {"status_code": 400, "message": "Passkey is required."}

    # Check in Passkey child table
    passkey_doc = frappe.db.get_value(
        "Vendor Pass Key",
        {"passkey": passkey},
        ["parent", "name"], 
        as_dict=True
    )

    if not passkey_doc:
        return {"status_code": 401, "message": "Invalid passkey."}

    vendor_id = passkey_doc.get("parent")

    return {
        "status_code": 200,
        "message": "Driver authenticated successfully.",
        "vendor_id": vendor_id,
        "passkey_row_id": passkey
    }



def generate_unique_passkey(short_name):
    """Generate a unique 3-digit key not used in any Vendor Passkey child table"""
    while True:
        if short_name != "":
            new_key = f"{short_name}-{random.randint(100, 999)}"
        else:
            new_key = f"{random.randint(100, 999)}"

        exists = frappe.db.exists("Vendor Pass Key", {"passkey": new_key})
        if not exists:
            return new_key


@frappe.whitelist()
@role_required(["Vendor"])
def add_passkey():
    """
    Add a Passkey entry to a Vendor's child table
    after regenerating a unique key.
    """
    vendor_user = frappe.session.user
    vendor_doc = frappe.get_doc("Vendor", {"email_id": vendor_user})

    if not vendor_doc:
        return {"status_code": 400, "message": "Vendor not found!"}
    
    short_name = vendor_doc.short_name if vendor_doc.short_name else ""

    # Generate a new unique key
    new_key = generate_unique_passkey(short_name)

    # Append child row in Passkey table
    vendor_doc.append("pass_key", {
        "passkey": new_key
    })

    # Save changes
    vendor_doc.save(ignore_permissions=True)
    frappe.db.commit()

    return {
        "status_code": 200,
        "message": f"Passkey added to Vendor {vendor_doc.name}",
        "vendor_id": vendor_doc.name,
        "passkey": new_key
    }