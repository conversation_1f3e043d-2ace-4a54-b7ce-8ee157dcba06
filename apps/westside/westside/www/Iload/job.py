import frappe
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Vendor"])
def get_job_by_booking_number(carrier_booking_number: str, vendor_id: str):
    """
    Fetch Job details for a given Vendor and Carrier Booking Number.
    """

    if not carrier_booking_number or not vendor_id:
        return {"status_code": 400, "message": "Both carrier_booking_number and vendor_id are required."}

    # Look up Job by vendor and carrier booking number
    job = frappe.get_value(
        "Job",
        {"vendor_name": vendor_id, "carrier_booking_number": carrier_booking_number},
        ["name", "vendor_name"],
        as_dict=True
    )

    if not job:
        return {
            "status_code": 404,
            "message": "No Job found for given Vendor and Carrier Booking Number."
        }

    return {
        "status_code": 200,
        "message": "Job found successfully.",
        "job": job
    }
