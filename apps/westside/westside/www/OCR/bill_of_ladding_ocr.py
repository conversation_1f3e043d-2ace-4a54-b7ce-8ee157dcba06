import frappe
import fitz  # PyMuPDF
from openai import OpenAI
import os
import json
from frappe.utils.file_manager import save_file
from westside.www.Authentication.auth_decorators import role_required
from westside.www.OCR.utils import normalize_weight_unit, normalize_volume_unit


@frappe.whitelist(methods=["POST"])
# @role_required(["Admin"])
def extract_bill_of_lading():
    """
    Upload a PDF Bill of Lading directly via API,
    extract text and return structured JSON using GPT-4.1 Mini.
    """

    # Uploaded file
    uploaded_file = frappe.request.files.get("file")
    if not uploaded_file:
        return { "status_code": 400, "message": "No file uploaded. Please upload a PDF file with key 'file'."}
    
    # Save uploaded file temporarily
    file_path = os.path.join(frappe.get_site_path("private", "files"), uploaded_file.filename)
    with open(file_path, "wb") as f:
        f.write(uploaded_file.read())

    # Extract raw text from PDF
    text_blocks = []
    with fitz.open(file_path) as doc:
        for page in doc:
            text_blocks.append(page.get_text("text"))
    raw_text = "\n".join(text_blocks)

    # Prepare OpenAI client
    client = OpenAI(api_key=frappe.conf.get("openai_api_key")) 

    # Prompt GPT
    prompt = f"""
    You are a data extractor. Convert the following Bill of Lading text
    into structured JSON. For each field listed below, always return a key,
    and if no value is found, set it to an empty string ("").
    Look for carrier booking number, the value is from Booking no (booking number) else Carrier’s Reference else carrier booking number
    Look for booking number, the value is from Booking no (booking number) else Carrier’s Reference.
    Look for bill of lading number, the value is from b/l no (bill of lading number).
    Movement Types are port to port, port to door, door to port and door to door.
    HS_Code should be first six letters.
    Total_packages  is sum up of all packages in container list, return string.
    Convert all date to datetime format.
    Carrier booking number and bill of lading number should not be same. Carrier booking number should be carrier reference. 
    The keys must be exactly as provided here (use snake_case):

    - bill_of_lading_number
    - carrier_booking_number
    - booking_number
    - date
    - shipped_on_board
    - date_issued_or_received_for_shipment
    - total_containers
    - total_packages
    - total_gross_weight
    - total_gross_volume
    - hs_code
    - hs_code_description
    - port_of_loading
    - port_of_discharge
    - main_voyage
    - main_vessel
    - shipment_id
    - contract_number
    - place_of_receipt
    - place_of_delivery
    - movement_type
    - service_type
    - transport_mode
    - freight_payment_date
    - freight_payment_location
    - carrier
    - document_type
    - document_number
    - created_datetime
    - lloyds_code
    - shipper
    - shipper_name
    - shipper_address
    - shipper_email
    - carrier_name
    - consignee
    - consignee_name
    - consignee_address
    - notify_address
    - vessel
    - voyage_number
    - container_details (list of objects with keys: container_no, seal_no, packages, weight, measurement, description)
    - issued_place
    - issued_date

    Fields to extract:
    - bill_of_lading_number
    - carrier_booking_number or carrier reference
    - booking no or booking number
    - date
    - shipped_on_board
    - date_issued_or_received_for_shipment
    - total_containers
    - total_packages
    - total_gross_weight
    - total_gross_volume
    - hs_code
    - hs_code_description
    - port_of_loading
    - port_of_discharge
    - main_voyage
    - main_vessel
    - shipment_id
    - contract_no
    - place_of_receipt
    - place_of_delivery
    - movement_type
    - service_type
    - transport_mode
    - freight_payment_date
    - freight_payment_location
    - carrier
    - document_type
    - document_number
    - created_datetime
    - lloyds_code
    - shipper
    - shipper_name
    - shipper_address
    - shipper_email
    - carrier_name
    - consignee
    - consignee_name
    - consignee_address
    - consignee_email
    - notify_address
    - vessel
    - voyage_no
    - container_details (list of objects with keys: container_no, seal_no, packages, weight, measurement, description)
    - issued_place
    - issued_date

    Bill of Lading text:
    {raw_text}
    """

    # Call GPT
    response = client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=[{"role": "user", "content": prompt}],
        response_format={"type": "json_object"}
    )

    # Parse JSON response safely
    try:
        structured_data = json.loads(response.choices[0].message.content)
    except Exception:
        frappe.throw("Failed to parse GPT response as JSON")
    
    uploaded_file.stream.seek(0)

    # Process the extracted data
    bol_doc_name = process_bol_data(structured_data, uploaded_file)
    structured_data['bol_doc_name'] = bol_doc_name

    # Return as Frappe JSON response
    frappe.response["status"] = "success"
    frappe.response["data"] = structured_data
    return


def process_bol_data(data, uploaded_file):
    """
    Process the OpenAI extracted data and create/update Bill of Lading DocType
    """
    # Get booking numbers to search for existing BOL
    booking_number = data.get("booking_number", "")
    carrier_booking_number = data.get("carrier_booking_number", "")
    
    # Search for existing BOL using booking number or carrier booking number
    existing_bol = None
    
    if booking_number:
        existing_bol = frappe.db.get_value("Bill of Lading", 
                                         {"carrier_booking_number": booking_number}, 
                                         "name")
    
    if not existing_bol and carrier_booking_number:
        existing_bol = frappe.db.get_value("Bill of Lading", 
                                         {"carrier_booking_number": carrier_booking_number}, 
                                         "name")
   
    # Get Booking Request data if carrier booking number exists
    booking_request_doc = None
    inttra_booking_number = ""
    
    if carrier_booking_number:
        booking_request = frappe.db.get_value("Booking Request", 
                                            {"carrier_booking_number": carrier_booking_number}, 
                                            ["name", "inttra_booking_id"], 
                                            as_dict=True)

        if booking_request:
            booking_request_doc = booking_request.name
            inttra_booking_number = booking_request.inttra_booking_id or ""

    if existing_bol:
        # Update existing BOL
        bol_doc_name = update_existing_bol(existing_bol, data, booking_request_doc, inttra_booking_number, uploaded_file)
    else:
        # Create new BOL
        bol_doc_name = create_new_bol(data, booking_request_doc, inttra_booking_number, uploaded_file)

    return bol_doc_name


def update_existing_bol(bol_name, data, booking_request_doc, inttra_booking_number, uploaded_file):
    """
    Update existing Bill of Lading with new data from OpenAI
    """
    try:
        bol_doc = frappe.get_doc("Bill of Lading", bol_name)
       
        # Save and attach file (pdf) to bill of lading
        site_url = frappe.utils.get_url()

        file_doc = save_file(
                uploaded_file.filename,
                uploaded_file.stream.read(),
                "Bill of Lading",
                bol_doc.name,
                is_private=0
            )
            
        file_url = frappe.utils.get_url(file_doc.file_url)

        # Append to child table "BOL Attachments"
        attachment_row = bol_doc.append("bol_attachments", {})
        attachment_row.file_name = uploaded_file.filename 
        attachment_row.file_url = file_url 
        bol_doc.save(ignore_permissions=True)


        # Update fields only if they have values from OpenAI
        field_mapping = {
            "bol_number": data.get("bill_of_lading_number"),
            "document_identifier": data.get("bill_of_lading_number"),
            "carrier_booking_number": data.get("carrier_booking_number"),
            "document_date": data.get("date"),  # date time
            "shipped_on_board_date": data.get("shipped_on_board"),  # date time
            "received_for_shipment": data.get("date_issued_or_received_for_shipment"),
            "total_equipment": data.get("total_containers"),
            "total_packages": data.get("total_packages"),
            "total_gross_weight": data.get("total_gross_weight"),
            "total_gross_volume": data.get("total_gross_volume"),
            "hs_code": data.get("hs_code"),
            "hs_code_description": data.get("hs_code_description"),
            "port_of_load_location": data.get("port_of_loading"),
            "port_of_discharge_location": data.get("port_of_discharge"),
            "main_voyage": data.get("main_voyage"),
            "main_vessel": data.get("main_vessel"),
            "shipment_id": data.get("shipment_id"),
            "contract_number": data.get("contract_no"),
            "place_of_receipt": data.get("place_of_receipt"),
            "place_of_delivery": data.get("place_of_delivery"),
            "movement_type": data.get("movement_type"),
            "service_type": "FullLoad",
            "transport_mode": "Maritime",
            "freight_payment_date": data.get("freight_payment_date"),
            "freight_payment_location": data.get("freight_payment_location"),
            "carrier": data.get("carrier"),
            "document_type": data.get("document_type"),
            "document_number": data.get("document_number"),
            "create_date_time": data.get("created_datetime"),
            "lloyds_code": data.get("lloyds_code"),
            # "issued_place": data.get("issued_place"),
            # "issued_date": data.get("issued_date")
        }
        
        # Update fields only if they have values
        for field, value in field_mapping.items():
            if value and value.strip():
                setattr(bol_doc, field, value)

        
        # parties - child table
        party_mappings = ["Shipper", "Consignee", "Carrier", "Contract Party"]

        for party in party_mappings:

            party_row = next((row for row in bol_doc.parties if row.partner_role == party), None)

            if not party_row:
                party_row = bol_doc.append("parties", {})
                party_row.partner_role = party

            if party == "Shipper":
                if data.get("shipper_name"):
                    party_row.partner_name = data.get("shipper_name").strip()
                if data.get("shipper_email"):
                    party_row.email = data.get("shipper_email").strip()
                if data.get("shipper_address"):
                    party_row.address_line = data.get("shipper_address").strip()

            elif party == "Consignee":
                if data.get("consignee_name"):
                    party_row.partner_name = data.get("consignee_name").strip()
                if data.get("consignee_email"):
                    party_row.email = data.get("consignee_email").strip()
                if data.get("consignee_address"):
                    party_row.address_line = data.get("consignee_address").strip()

            elif party == "Carrier":
                if data.get("carrier_name"):
                    party_row.partner_name = data.get("carrier_name").strip()

            elif party == "Contract Party":
                if data.get("consignee_name"):
                    party_row.partner_name = data.get("consignee_name").strip()
                if data.get("consignee_email"):
                    party_row.email = data.get("consignee_email").strip()
                if data.get("consignee_address"):
                    party_row.address_line = data.get("consignee_address").strip()

            # If no data was set (all fields empty), remove the blank row
            if not (party_row.partner_name or party_row.email or party_row.address_line):
                bol_doc.parties.remove(party_row)
        
        bol_doc.save()
        
        # Update equipment table
        update_equipment_table(bol_doc, data, booking_request_doc, inttra_booking_number)
        
        bol_doc.save()
        frappe.db.commit()
        
        frappe.msgprint(f"Updated existing Bill of Lading: {bol_name}")
        return bol_doc.name
        
    except Exception as e:
        return { "status_code": 400, "message": f"Error updating Bill of Lading: {str(e)}"}


def create_new_bol(data, booking_request_doc, inttra_booking_number, uploaded_file):
    """
    Create new Bill of Lading with data from OpenAI
    """
    try:
        bol_doc = frappe.new_doc("Bill of Lading")

        # Set all fields from the data
        field_mapping = {
            "bol_number": data.get("bill_of_lading_number"),
            "document_identifier": data.get("bill_of_lading_number"),
            "carrier_booking_number": data.get("carrier_booking_number"),
            # "booking_number": data.get("booking_number"),
            "document_date": data.get("date"),  # date time issue
            "shipped_on_board_date": data.get("shipped_on_board"),  # date time issue
            "received_for_shipment": data.get("date_issued_or_received_for_shipment"),
            "total_equipment": data.get("total_containers"),
            "total_packages": data.get("total_packages"),
            "total_gross_weight": data.get("total_gross_weight"),
            "total_gross_volume": data.get("total_gross_volume"),
            "hs_code": data.get("hs_code"),
            "hs_code_description": data.get("hs_code_description"),
            "port_of_load_location": data.get("port_of_loading"),
            "port_of_discharge_location": data.get("port_of_discharge"),
            "main_voyage": data.get("main_voyage"),
            "main_vessel": data.get("main_vessel"),
            "shipment_id": data.get("shipment_id"),
            "contract_number": data.get("contract_no"),
            "place_of_receipt": data.get("place_of_receipt"),
            "place_of_delivery": data.get("place_of_delivery"),
            "movement_type": data.get("movement_type"),
            "service_type": "FullLoad",
            "transport_mode": "Maritime",
            "freight_payment_date": data.get("freight_payment_date"),
            "freight_payment_location": data.get("freight_payment_location"),
            "carrier": data.get("carrier"),
            "document_type": data.get("document_type"),
            "document_number": data.get("document_number"),
            "create_date_time": data.get("created_datetime"),
            "lloyds_code": data.get("lloyds_code"),
            # "issued_place": data.get("issued_place"),
            # "issued_date": data.get("issued_date")
        }
        
        # Set fields with values
        for field, value in field_mapping.items():
            if value and value.strip():
                setattr(bol_doc, field, value)

        
        # parties - child table
        party_mappings = ["Shipper", "Consignee", "Carrier", "Contract Party"]

        for party in party_mappings:
            
            party_row = next((row for row in bol_doc.parties if row.partner_role == party), None)

            if not party_row:
                party_row = bol_doc.append("parties", {})
                party_row.partner_role = party

            if party == "Shipper":
                if data.get("shipper_name"):
                    party_row.partner_name = data.get("shipper_name").strip()
                if data.get("shipper_email"):
                    party_row.email = data.get("shipper_email").strip()
                if data.get("shipper_address"):
                    party_row.address_line = data.get("shipper_address").strip()

            elif party == "Consignee":
                if data.get("consignee_name"):
                    party_row.partner_name = data.get("consignee_name").strip()
                if data.get("consignee_email"):
                    party_row.email = data.get("consignee_email").strip()
                if data.get("consignee_address"):
                    party_row.address_line = data.get("consignee_address").strip()

            elif party == "Carrier":
                if data.get("carrier_name"):
                    party_row.partner_name = data.get("carrier_name").strip()

            elif party == "Contract Party":
                if data.get("consignee_name"):
                    party_row.partner_name = data.get("consignee_name").strip()
                if data.get("consignee_email"):
                    party_row.email = data.get("consignee_email").strip()
                if data.get("consignee_address"):
                    party_row.address_line = data.get("consignee_address").strip()

            # If no data was set (all fields empty), remove the blank row
            if not (party_row.partner_name or party_row.email or party_row.address_line):
                bol_doc.parties.remove(party_row)
               
        bol_doc.insert()
        
        # Add equipment table data
        update_equipment_table(bol_doc, data, booking_request_doc, inttra_booking_number)
        
        bol_doc.insert()

        # Save and attach file (pdf) to bill of lading
        site_url = frappe.utils.get_url()
        file_doc = save_file(
                uploaded_file.filename,
                uploaded_file.stream.read(),
                "Bill of Lading",
                bol_doc.name,
                is_private=0
            )
            
        file_url = frappe.utils.get_url(file_doc.file_url)

        # Append to child table "BOL Attachments"
        attachment_row = bol_doc.append("bol_attachments", {})
        attachment_row.file_name = uploaded_file.filename 
        attachment_row.file_url = file_url 
        bol_doc.save(ignore_permissions=True)
        frappe.db.commit()
        
        frappe.msgprint(f"Created new Bill of Lading: {bol_doc.name}")
        return bol_doc.name

    except Exception as e:
        return { "status_code": 400, "message": f"Error creating Bill of Lading: {str(e)}"}




def update_equipment_table(bol_doc, data, booking_request_doc, inttra_booking_number):
    """
    Update or create Equipment documents for each container detail.
    Match by (equipment_name, carrier_booking_number, bill_of_lading).
    Only update fields if they have actual values - don't overwrite with empty strings.
    """
    container_details = data.get("container_details", [])
    carrier_booking_number = data.get("carrier_booking_number", "")
    
    for container in container_details:
        container_no = container.get("container_no", "")
        if not container_no:
            continue

        # Check if equipment already exists
        existing_equipment = frappe.get_all(
            "Equipments",
            filters={
                "equipment_name": container_no,
                "carrier_booking_number": carrier_booking_number
            },
            limit=1
        )

        if existing_equipment:
            equipment_doc = frappe.get_doc("Equipments", existing_equipment[0].name)
        else:
            equipment_doc = frappe.new_doc("Equipments")
            equipment_doc.bill_of_lading_id = bol_doc.name  # link to BOL

        # Update fields only if they have values - don't overwrite with empty strings
        equipment_doc.equipment_name = container_no  
        
        seal_no = container.get("seal_no", "")
        if seal_no and seal_no.strip():
            equipment_doc.shipper_seal_number = seal_no
        
        weight = container.get("weight", "")
        if weight and weight.strip():
            equipment_doc.gross_weight = weight
        
        measurement = container.get("measurement", "")
        if measurement and measurement.strip():
            equipment_doc.gross_volume = measurement
        
        if carrier_booking_number and carrier_booking_number.strip():
            equipment_doc.carrier_booking_number = carrier_booking_number

        if booking_request_doc:
            equipment_doc.booking_request = booking_request_doc
        
        if inttra_booking_number and inttra_booking_number.strip():
            equipment_doc.inttra_booking_number = inttra_booking_number

        # --- Cargo child table ---
        # Only clear and update cargo if we have container details with actual data
        has_cargo_data = any([
            container.get("packages", ""),
            data.get("hs_code", ""),
            data.get("hs_code_description", ""),
            data.get("total_gross_weight", ""),
            data.get("total_gross_volume", "")
        ])
        
        if has_cargo_data:
            equipment_doc.set("cargo", []) 
            cargo_row = equipment_doc.append("cargo", {})
            cargo_row.container_number = container_no

            # Parse package data
            package_data = container.get("packages", "")
            if package_data and package_data.strip():
                package_count, package_type = "", ""
                parts = package_data.split(" ", 1)
                if parts:
                    package_count = parts[0]
                if len(parts) > 1:
                    package_type = parts[1]
                
                if package_count:
                    cargo_row.package_count = package_count
                
                if package_type:
                    # Check if a Packed Type with this standard_name exists
                    try:
                        packed_type_name = frappe.db.get_value("Packed Type", {"standard_name": package_type}, "name")
                        
                        if packed_type_name:
                            cargo_row.package_counttype_outermost = packed_type_name
                            cargo_row.print_on_bl_as = packed_type_name
                    except Exception:
                        pass
            
            # Parse and set gross weight with unit normalization
            total_gross_weight = data.get("total_gross_weight", "")
            if total_gross_weight and total_gross_weight.strip():
                # Handle different weight formats: "259100.000KGS" or "259100.000 KGS"
                weight_str = total_gross_weight.replace("KGS", " KGS").replace("KGM", " KGM").strip()
                parts = weight_str.rsplit(" ", 1) 
                
                gross_weight_value = ""
                gross_weight_unit = ""
                
                if len(parts) == 1:
                    # No unit found, assume it's just the value
                    gross_weight_value = parts[0]
                    gross_weight_unit = "KG"  # Default unit
                else:
                    gross_weight_value = parts[0]
                    gross_weight_unit = normalize_weight_unit(parts[1])
                
                if gross_weight_value:
                    try:
                        # Validate it's a number
                        float(gross_weight_value)
                        cargo_row.net_weight = gross_weight_value
                        equipment_doc.weight_value = gross_weight_value
                    except ValueError:
                        # If not a valid number, skip setting weight
                        pass
                
                if gross_weight_unit:
                    cargo_row.net_weight_unit = gross_weight_unit
                    equipment_doc.weight_type = gross_weight_unit
                
                cargo_row.cargo_gross_weight = total_gross_weight

            # Parse and set gross volume with unit normalization
            total_gross_volume = data.get("total_gross_volume", "")
            if total_gross_volume and total_gross_volume.strip():
                # Handle different volume formats: "760.000CBM" or "760.000 CBM"
                volume_str = total_gross_volume.replace("CBM", " CBM").replace("CBF", " CBF").strip()
                parts = volume_str.rsplit(" ", 1) 
                
                gross_volume_value = ""
                gross_volume_unit = ""
                
                if len(parts) == 1:
                    # No unit found, assume it's just the value
                    gross_volume_value = parts[0]
                    gross_volume_unit = "CBM"  # Default unit
                else:
                    gross_volume_value = parts[0]
                    gross_volume_unit = normalize_volume_unit(parts[1])
                
                if gross_volume_value:
                    try:
                        # Validate it's a number
                        float(gross_volume_value)
                        cargo_row.gross_volume = gross_volume_value
                    except ValueError:
                        # If not a valid number, skip setting volume
                        pass
                
                if gross_volume_unit:
                    cargo_row.gross_volume_unit = gross_volume_unit
                    equipment_doc.volume_unit = gross_volume_unit

            # Set other cargo fields only if they have values
            hs_code = data.get("hs_code", "")
            if hs_code and hs_code.strip():
                cargo_row.hs_code = hs_code
            
            # Get description from container or main data
            description = data.get("hs_code_description", "")
            if description and description.strip():
                cargo_row.cargo_description = description
                equipment_doc.description = description

            equipment_doc.save(ignore_permissions=True)
      
