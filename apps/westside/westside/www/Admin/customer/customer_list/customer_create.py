import frappe
from frappe import _
import json
from frappe.utils.file_manager import save_file
from westside.www.Authentication.auth_decorators import role_required




@frappe.whitelist()
@role_required(["Admin"])
def create_customer():
    import json
    try:
        data_str = frappe.form_dict.get("data")
        license_attachments = frappe.request.files.getlist("license_attachments") or []

        # parse payload
        try:
            data = json.loads(data_str) if data_str else {}
        except json.JSONDecodeError as e:
            return {"status_code": 400, "message": f"Invalid JSON format: {str(e)}"}

        # fields
        first_name = data.get("first_name")
        last_name = data.get("last_name")
        customer_name = data.get("company_name")
        company_name = data.get("company_name")
        parent_company = data.get("parent_company")
        contact = data.get("contact")
        phone = data.get("phone")
        email_id = data.get("email_id")
        customer_zip = data.get("customer_zip")
        customer_city = data.get("customer_city")
        customer_state = data.get("customer_state")
        customer_country = data.get("customer_country")
        tax_id1 = data.get("tax_id1")
        tax_id2 = data.get("tax_id2")
        tax_id3 = data.get("tax_id3")
        customer_free_form_address = data.get("customer_free_form_address")
        notification_email = data.get("notification_email")
        flag_for_custom_docs = data.get("flag_for_custom_docs")
        street_name = data.get("street_name")
        street_number = data.get("street_number")
        po_box = data.get("po_box")

        # Check duplicate company_name
        if company_name:
            if frappe.db.exists("Customer DB", {"company_name": company_name}):
                return {
                    "status_code": 409,
                    "message": f"Customer with company name '{company_name}' already exists."
                }

        # Validate country code (if provided)
        country_name = ""
        if customer_country:
            # get_value returns the value of the 'name' by default if second arg not provided,
            # but keep the existing behavior from your code:
            country_doc = frappe.db.get_value("Country", {"code": customer_country.lower()})
            if not country_doc:
                return {
                    "status_code": 404,
                    "message": f"Invalid country code: '{customer_country.lower()}'."
                }
            country_name = country_doc

        # Build and insert customer (no attachments yet)
        customer = frappe.get_doc({
            "doctype": "Customer DB",
            "first_name": first_name,
            "last_name": last_name,
            "customer_name": customer_name,
            "company_name": company_name,
            "parent_company": parent_company,
            "phone": phone,
            "contact": contact,
            "email_id": email_id,
            "customer_zip": customer_zip,
            "customer_city": customer_city,
            "customer_state": customer_state,
            "customer_country": country_name,
            "tax_id1": tax_id1,
            "tax_id2": tax_id2,
            "tax_id3": tax_id3,
            "customer_address": customer_free_form_address,
            "customer_free_form_address": customer_free_form_address,
            "notification_email": notification_email,
            "flag_for_custom_docs": flag_for_custom_docs,
            "street_name": street_name,
            "street_number": street_number,
            "po_box": po_box
        })

        # Insert to DB to get a valid name
        customer.insert(ignore_permissions=True)
        frappe.db.commit()

        # If there are attachments, refetch fresh doc and attach them
        if license_attachments:
            site_url = frappe.utils.get_url()
            uploaded_files = []  # track file doc names so we can cleanup on failure
            MAX_RETRIES = 3

            for attempt in range(MAX_RETRIES):
                try:
                    # fetch the latest document (fresh timestamp)
                    fresh_customer = frappe.get_doc("Customer DB", customer.name)

                    # upload each file and append to the freshly fetched doc
                    for license_file in license_attachments:
                        # read content (support both .read() and .stream.read())
                        try:
                            file_content = license_file.read()
                        except Exception:
                            file_content = license_file.stream.read()

                        # save_file attaches the File to the customer record
                        file_doc = save_file(
                            license_file.filename,
                            file_content,
                            "Customer DB",
                            fresh_customer.name,
                            is_private=0
                        )

                        # remember uploaded file id for cleanup if needed
                        uploaded_files.append(getattr(file_doc, "name", file_doc.get("name") if isinstance(file_doc, dict) else None))

                        # append child row (use file URL for UI)
                        file_url = getattr(file_doc, "file_url", file_doc.get("file_url") if isinstance(file_doc, dict) else None)
                        fresh_customer.append("license_attachments", {
                            "license": site_url + file_url if file_url else (file_doc.name if hasattr(file_doc, "name") else ""),
                            "file_name": license_file.filename
                        })

                    # save the fresh doc once after appending all child rows
                    fresh_customer.save(ignore_permissions=True)
                    frappe.db.commit()
                    break  # success -> exit retry loop

                except frappe.exceptions.TimestampMismatchError:
                    # someone modified the doc between our fetch and save; cleanup uploaded files and retry
                    for f in uploaded_files:
                        if f:
                            try:
                                frappe.delete_doc("File", f, force=True)
                            except Exception:
                                pass
                    uploaded_files = []
                    # If last attempt, rollback the created customer and return error
                    if attempt == MAX_RETRIES - 1:
                        try:
                            frappe.delete_doc("Customer DB", customer.name, force=True)
                            frappe.db.commit()
                        except Exception:
                            pass
                        return {
                            "status_code": 500,
                            "message": "Failed to attach files due to concurrent modification. Created customer has been rolled back."
                        }
                    # otherwise loop and retry

                except Exception as e:
                    # unexpected error while uploading/attaching: cleanup and rollback
                    for f in uploaded_files:
                        if f:
                            try:
                                frappe.delete_doc("File", f, force=True)
                            except Exception:
                                pass
                    try:
                        frappe.delete_doc("Customer DB", customer.name, force=True)
                        frappe.db.commit()
                    except Exception:
                        pass

                    frappe.log_error(frappe.get_traceback(), "Create Customer API Error")
                    return {
                        "status_code": 500,
                        "message": str(e)
                    }

        # success
        return {
            "status_code": 201,
            "message": "Customer created successfully",
            "data": {"customer_name": customer.name}
        }

    except json.JSONDecodeError:
        frappe.response["http_status_code"] = 400
        frappe.response["message"] = {"error": "Invalid JSON format."}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Create Customer API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}





@frappe.whitelist()
@role_required(["Admin"])
def get_parent_companies():
    try:
        parent_companies = frappe.get_all(
            "Customer DB",
            fields=["company_name"]
        )

        company_list = [d["company_name"] for d in parent_companies if d["company_name"]]

        return {"data" :set(company_list)}

    except Exception as e:
        frappe.log_error(f"Error in get_parent_companies: {str(e)}")
        frappe.throw("Something went wrong while fetching parent companies.")
