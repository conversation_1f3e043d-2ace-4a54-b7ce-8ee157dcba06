import frappe
from frappe.model.document import Document
import os
from datetime import datetime
import paramiko
import xml.etree.ElementTree as ET
from frappe.utils import get_datetime
import traceback
from westside.westside.doctype.bill_of_lading.bill_of_lading import process_webbl_exml_files, process_webbl_pdf_files
import zipfile
import time

from westside.westside.doctype.evgm.evgm import process_evgm_response_files

# @frappe.whitelist(allow_guest=True) 
# def start_sftp_watcher():
#     count = 0
#     while True:
#         start_time = time.time()
#         count += 1
#         try:
#             frappe.logger().info("🔄 Running SFTP watcher...")
#             print(f"▶️ Execution #{count} started")
#             download_xml_from_sftp()
#         except Exception as e:
#             frappe.log_error("SFTP Watcher Error", str(e))

#         # calculate execution time
#         elapsed = time.time() - start_time
#         print(f"✅ Execution #{count} completed in {elapsed:.2f} seconds")

#         # sleep = execution time + 30s
#         sleep_time = elapsed + 30
#         print(f"⏸ Sleeping for {sleep_time:.2f} seconds before next run\n")
#         time.sleep(sleep_time)

@frappe.whitelist(allow_guest=True)
def download_xml_from_sftp():
    """Download XML and ZIP files from SFTP server and process them"""
    sftp = None
    transport = None
    frappe.logger().info("\u2705 Cron job: download_xml_from_sftp ran")

    try:
        sftp_settings = frappe.get_doc("SFTP Server Settings")
        host = sftp_settings.host
        username = sftp_settings.username
        password = sftp_settings.get_password("password")
        port = sftp_settings.port or 22

        transport = paramiko.Transport((host, port))
        transport.connect(username=username, password=password)
        sftp = paramiko.SFTPClient.from_transport(transport)

        remote_path_xml = "outbound/"
        remote_path_zip = "outbound/webbl/"
        remote_path_vgm = "outbound/vgm/"
        # remote_path_vgm = "outbound_archive/VGM_APERAK/20250909/"
        local_folder = frappe.get_site_path("private", "sftp_downloads")
        os.makedirs(local_folder, exist_ok=True)
        today = datetime.now().date()
        
        frappe.logger().info(f"🔍 Checking files downloaded on {today}")
        already_downloaded = set([
            d.file_name for d in frappe.get_all("SFTP Downloaded Files",
                                                filters={"downloaded_on": ["between", [today, today]]},
                                                fields=["file_name"])
        ])
        
        xml_files = sftp.listdir(remote_path_xml)
        zip_files = sftp.listdir(remote_path_zip)
        vgm_files = sftp.listdir(remote_path_vgm)
        files = xml_files + vgm_files
        frappe.logger().info(f"📁 Found {len(files)} files on SFTP server")
        
        downloaded_files = {
            'webbl': [],
            'aperak': [],
            'contrlx': [],
            'webbl_pdf': [],
            'vgm': []
        }
        to_delete = []
        for file in files:
            if file in ['webbl', 'vgm'] or file in already_downloaded:
                continue

            if file in xml_files:
                full_remote = f"{remote_path_xml}{file}"
            if file in vgm_files:
                full_remote = f"{remote_path_vgm}{file}"
            elif file in zip_files:
                full_remote = f"{remote_path_zip}{file}"
            else:
                continue

            file_name = os.path.basename(file)
            ext = os.path.splitext(file_name)[1].lower()
            local_path = os.path.join(local_folder, file_name)

            try:
                with sftp.file(full_remote, 'rb') as remote_file:
                    file_bytes = remote_file.read()

                with open(local_path, 'wb') as f:
                    f.write(file_bytes)

                file_url = None
                if ext in [".xml", ".XML"]:
                    file_doc = frappe.get_doc({
                        "doctype": "File",
                        "file_name": file_name,
                        "is_private": 0,
                        "attached_to_doctype": None,
                        "attached_to_name": None,
                        "content": file_bytes,
                    }).insert(ignore_permissions=True)
                    file_url = file_doc.file_url

                frappe.get_doc({
                    "doctype": "SFTP Downloaded Files",
                    "file_name": file_name,
                    "downloaded_on": today,
                    "downloaded_time": datetime.now().time(),
                    "file_type": file_name[:12] if len(file_name) >= 12 else file_name,
                    "file_path": file_url or local_path,
                    "status": "Downloaded"
                }).insert(ignore_permissions=True)
                frappe.db.commit()
                frappe.logger().info(f"⬇️ Downloaded: {file_name}")

                if ext in [".xml", ".XML"]:
                    prefix = file_name[:12]
                    if prefix == "WESEL_IFTMCS":
                        downloaded_files['webbl'].append(file_name)
                    if prefix == "WESEL_APERAK":
                        downloaded_files['aperak'].append(local_path)
                    if prefix == "WESEL_CONTRL":
                        downloaded_files['contrlx'].append(local_path)
                    if file_name in vgm_files:
                        downloaded_files['vgm'].append(local_path)
                    to_delete.append(full_remote)

            except Exception as e:
                error_msg = f"❌ Failed downloading {file_name}: {str(e)}"
                frappe.log_error("SFTP File Download Error", error_msg)
                try:
                    frappe.get_doc({
                        "doctype": "SFTP Downloaded Files",
                        "file_name": file_name,
                        "downloaded_on": today,
                        "downloaded_time": datetime.now().time(),
                        "file_type": file_name[:12] if len(file_name) >= 12 else file_name,
                        "file_path": local_path,
                        "status": "Failed"
                    }).insert(ignore_permissions=True)
                    frappe.db.commit()
                except:
                    pass
     
        processing_results = {
            'webbl_processed': 0,
            'aperak_processed': 0,
            'contrlx_processed': 0,
            'errors': []
        }

        if downloaded_files['webbl']:
            try:
                process_webbl_exml_files(downloaded_files['webbl'])
                processing_results['webbl_processed'] = len(downloaded_files['webbl'])
            except Exception as e:
                msg = f"❌ Failed processing WESEL_IFTMCS: {str(e)}"
                frappe.log_error("WESEL_IFTMCS Error", msg)
                processing_results['errors'].append(msg)

        for file_path in downloaded_files['aperak']:
            try:
                process_aperak_file(file_path, os.path.basename(file_path))
                processing_results['aperak_processed'] += 1
            except Exception as e:
                msg = f"❌ Failed processing APERAK {file_path}: {str(e)}"
                frappe.log_error("APERAK Error", msg)
                processing_results['errors'].append(msg)

        for file_path in downloaded_files['contrlx']:
            try:
                process_contrlx_file(file_path, os.path.basename(file_path))
                processing_results['contrlx_processed'] += 1
            except Exception as e:
                msg = f"❌ Failed processing CONTRLX {file_path}: {str(e)}"
                frappe.log_error("CONTRLX Error", msg)
                processing_results['errors'].append(msg)
        if downloaded_files['vgm']:
            try:
                process_evgm_response_files(downloaded_files['vgm'])
            except Exception as e:
                msg = f"❌ Failed processing eVGM XML file: {str(e)}"
                frappe.log_error("eVGM XML Processing Error", msg)
                processing_results['errors'].append(msg)
        for remote_file in to_delete:
            try:
                sftp.remove(remote_file)
                print(f"Deleted from SFTP: {remote_file}")
            except Exception as e:
                frappe.log_error("SFTP Delete Error", f"Could not delete {remote_file}: {str(e)}")


        frappe.logger().info("\u2705 Scheduled job completed successfully")
        return {
            "status": "success",
            "message": "Files downloaded and processed successfully.",
            "details": processing_results
        }

    except Exception as e:
        error_msg = f"❌ SFTP Job Error: {str(e)}\n{traceback.format_exc()}"
        frappe.log_error("SFTP Download General Error", error_msg)
        return {"status": "error", "message": error_msg}

    finally:
        print("Gone to zip process")
        process_zip_file_from_sftp(zip_files,sftp,transport)


        


@frappe.whitelist(allow_guest=True)
def process_zip_file_from_sftp(zip_files,sftp,transport):
    """Download XML and ZIP files from SFTP server and process them"""

    try:
        print("innnnnn")
        remote_path_zip = "outbound/webbl/"
        local_folder = frappe.get_site_path("private", "sftp_downloads")
        os.makedirs(local_folder, exist_ok=True)
        today = datetime.now().date()
        
        frappe.logger().info(f"🔍 Checking files downloaded on {today}")
        already_downloaded = set([
            d.file_name for d in frappe.get_all("SFTP Downloaded Files",
                                                filters={"downloaded_on": ["between", [today, today]]},
                                                fields=["file_name"])
        ])


        downloaded_files = {
            'webbl': [],
            'aperak': [],
            'contrlx': [],
            'webbl_pdf': []
        }

        xml_pdf_map = {}
        lst_zip_files = []
        for file in zip_files:
            if file in already_downloaded:
                continue

            if file in zip_files:
                full_remote = f"{remote_path_zip}{file}"
            else:
                continue
            lst_zip_files.append(full_remote)
            file_name = os.path.basename(file)
            ext = os.path.splitext(file_name)[1].lower()
            local_path = os.path.join(local_folder, file_name)

            try:
                with sftp.file(full_remote, 'rb') as remote_file:
                    file_bytes = remote_file.read()

                with open(local_path, 'wb') as f:
                    f.write(file_bytes)

                file_url = None
                frappe.get_doc({
                    "doctype": "SFTP Downloaded Files",
                    "file_name": file_name,
                    "downloaded_on": today,
                    "downloaded_time": datetime.now().time(),
                    "file_type": file_name[:12] if len(file_name) >= 12 else file_name,
                    "file_path": file_url or local_path,
                    "status": "Downloaded"
                }).insert(ignore_permissions=True)
                frappe.db.commit()
                frappe.logger().info(f"⬇️ Downloaded: {file_name}")

                if ext == ".zip":
                    with zipfile.ZipFile(local_path, 'r') as zip_ref:
                        zip_ref.extractall(local_folder)
                        extracted_files = zip_ref.namelist()

                        if extracted_files[0].lower().endswith(".xml"):
                            xml_pdf_map[extracted_files[0]] = extracted_files[-1]
                        elif extracted_files[-1].lower().endswith(".xml"):
                            xml_pdf_map[extracted_files[-1]] = extracted_files[0]

                        if xml_pdf_map:
                            for xml_file, pdf_file in xml_pdf_map.items():
                                pdf_local_path = os.path.join(local_folder, pdf_file)
                                try:
                                    with open(pdf_local_path, 'rb') as f:
                                        pdf_bytes = f.read()
                                    file_doc = frappe.get_doc({
                                        "doctype": "File",
                                        "file_name": pdf_file,
                                        "is_private": 0,
                                        "attached_to_doctype": None,
                                        "attached_to_name": None,
                                        "content": pdf_bytes,
                                    }).insert(ignore_permissions=True)
                                    frappe.db.commit()
                                    frappe.logger().info(f"📎 Saved extracted PDF to File Doctype: {pdf_file}")
                                except Exception as e:
                                    frappe.log_error("PDF Extract Save Failed", f"{pdf_file}: {str(e)}")

                                xml_local_path = os.path.join(local_folder, xml_file)
                                try:
                                    with open(xml_local_path, 'rb') as f:
                                        xml_bytes = f.read()
                                    file_doc = frappe.get_doc({
                                        "doctype": "File",
                                        "file_name": xml_file,
                                        "is_private": 0,
                                        "attached_to_doctype": None,
                                        "attached_to_name": None,
                                        "content": xml_bytes,
                                    }).insert(ignore_permissions=True)
                                    frappe.db.commit()
                                    frappe.logger().info(f"📄 Saved extracted XML to File Doctype: {xml_file}")
                                except Exception as e:
                                    frappe.log_error("XML Extract Save Failed", f"{xml_file}: {str(e)}")
                            downloaded_files['webbl_pdf'].append(xml_pdf_map)

            except Exception as e:
                error_msg = f"❌ Failed downloading {file_name}: {str(e)}"
                frappe.log_error("SFTP File Download Error", error_msg)
                try:
                    frappe.get_doc({
                        "doctype": "SFTP Downloaded Files",
                        "file_name": file_name,
                        "downloaded_on": today,
                        "downloaded_time": datetime.now().time(),
                        "file_type": file_name[:12] if len(file_name) >= 12 else file_name,
                        "file_path": local_path,
                        "status": "Failed"
                    }).insert(ignore_permissions=True)
                    frappe.db.commit()
                except:
                    pass

        
        processing_results = {
            'webbl_processed': 0,
            'aperak_processed': 0,
            'contrlx_processed': 0,
            'errors': []
        }


        if xml_pdf_map:
            try:
                process_webbl_pdf_files(xml_pdf_map)
                processing_results['webbl_pdf_processed'] = len(downloaded_files['webbl_pdf'])
            except Exception as e:
                msg = f"❌ Failed processing webbl ZIP file: {str(e)}"
                frappe.log_error("webbl zip file Error", msg)
                processing_results['errors'].append(msg)
        
        for remote_file in lst_zip_files if lst_zip_files else []:
            try:
                sftp.remove(remote_file)
                print(f"Deleted from SFTP: {remote_file}")
            except Exception as e:
                frappe.log_error("SFTP Delete Error", f"Could not delete {remote_file}: {str(e)}")


        frappe.logger().info("\u2705 Scheduled job completed successfully")
        return {
            "status": "success",
            "message": "Files downloaded and processed successfully.",
            "details": processing_results
        }

    except Exception as e:
        error_msg = f"❌ SFTP Job Error: {str(e)}\n{traceback.format_exc()}"
        frappe.log_error("SFTP Download General Error", error_msg)
        return {"status": "error", "message": error_msg}

    finally:
        print("Closing SFTP connection...")
        try:
            if sftp:
                sftp.close()
        except:
            pass
        try:
            if transport:
                transport.close()
        except:
            pass



def process_evgm_files(vgm_files, sftp, transport):
    """Download XML files from SFTP server of evegm and process them"""
    try:
        remote_path_vgm = "outbound/vgm/"
        local_folder = frappe.get_site_path("private", "sftp_downloads")
        os.makedirs(local_folder, exist_ok=True)
        today = datetime.now().date()
        
        frappe.logger().info(f"🔍 Checking files downloaded on {today}")
        already_downloaded = set([
            d.file_name for d in frappe.get_all("SFTP Downloaded Files",
                                                filters={"downloaded_on": ["between", [today, today]]},
                                                fields=["file_name"])
        ])
    
        to_delete = []
        for file in vgm_files:
            if file in ['webbl', 'vgm'] or file in already_downloaded:
                continue

            if file in vgm_files:
                full_remote = f"{remote_path_vgm}{file}"
            else:
                continue

            file_name = os.path.basename(file)
            ext = os.path.splitext(file_name)[1].lower()
            local_path = os.path.join(local_folder, file_name)

            try:
                with sftp.file(full_remote, 'rb') as remote_file:
                    file_bytes = remote_file.read()

                with open(local_path, 'wb') as f:
                    f.write(file_bytes)

                file_url = None
                if ext in [".xml", ".XML"]:
                    file_doc = frappe.get_doc({
                        "doctype": "File",
                        "file_name": file_name,
                        "is_private": 0,
                        "attached_to_doctype": None,
                        "attached_to_name": None,
                        "content": file_bytes,
                    }).insert(ignore_permissions=True)
                    file_url = file_doc.file_url

                frappe.get_doc({
                    "doctype": "SFTP Downloaded Files",
                    "file_name": file_name,
                    "downloaded_on": today,
                    "downloaded_time": datetime.now().time(),
                    "file_type": file_name[:12] if len(file_name) >= 12 else file_name,
                    "file_path": file_url or local_path,
                    "status": "Downloaded"
                }).insert(ignore_permissions=True)
                frappe.db.commit()
                frappe.logger().info(f"⬇️ Downloaded: {file_name}")

                if ext in [".xml", ".XML"]:
                    to_delete.append(full_remote)

            except Exception as e:
                error_msg = f"❌ Failed downloading {file_name}: {str(e)}"
                frappe.log_error("SFTP File Download Error", error_msg)
                try:
                    frappe.get_doc({
                        "doctype": "SFTP Downloaded Files",
                        "file_name": file_name,
                        "downloaded_on": today,
                        "downloaded_time": datetime.now().time(),
                        "file_type": file_name[:12] if len(file_name) >= 12 else file_name,
                        "file_path": local_path,
                        "status": "Failed"
                    }).insert(ignore_permissions=True)
                    frappe.db.commit()
                except:
                    pass
      

        if vgm_files:
            try:
                process_evgm_response_files(vgm_files)
            except Exception as e:
                msg = f"❌ Failed processing eVGM XML file: {str(e)}"
                frappe.log_error("eVGM XML Processing Error", msg)

        for remote_file in to_delete:
            try:
                sftp.remove(remote_file)
                print(f"Deleted from SFTP: {remote_file}")
            except Exception as e:
                frappe.log_error("SFTP Delete Error", f"Could not delete {remote_file}: {str(e)}")

        frappe.logger().info("\u2705 Scheduled job completed successfully")
        
        return {
            "status": "success",
            "message": "Files downloaded and processed successfully."
        } 
        
    except Exception as e:
        error_msg = f"❌ SFTP Job Error: {str(e)}\n{traceback.format_exc()}"
        frappe.log_error("SFTP Download General Error", error_msg)
        return {"status": "error", "message": error_msg}









def download_xml_from_sftp_1():
    """Download XML and ZIP files from SFTP server and process them"""
    sftp = None
    transport = None
    frappe.logger().info("\u2705 Cron job: download_xml_from_sftp ran")
    try:

        # Setup SFTP connection
        sftp_settings = frappe.get_doc("SFTP Server Settings")
        host = sftp_settings.host
        username = sftp_settings.username
        password = sftp_settings.get_password("password")
        port = sftp_settings.port or 22

        transport = paramiko.Transport((host, port))
        transport.connect(username=username, password=password)
        sftp = paramiko.SFTPClient.from_transport(transport)
        
        # Define remote paths
        remote_path_xml = "outbound/"
        remote_path_zip = "outbound/webbl/"
        local_path = frappe.utils.get_site_path("private", "sftp_downloads")
        os.makedirs(local_path, exist_ok=True)
        today = datetime.now().date()
        frappe.logger().info(f"🔍 Checking files downloaded on {today}")
        already_downloaded = set([
            d.file_name for d in frappe.get_all("SFTP Downloaded Files", 
                                                filters={"downloaded_on": ["between", [today, today]]}, 
                                                fields=["file_name"])
        ])
        # List files from both paths
        xml_files = sftp.listdir(remote_path_xml)
        zip_files = sftp.listdir(remote_path_zip)
        files = xml_files + zip_files
        frappe.logger().info(f"📁 Found {len(files)} files on SFTP server")
        frappe.logger().info(f"XML files: {xml_files}")
        frappe.logger().info(f"ZIP files: {zip_files}")
        zip_files = sftp.listdir(remote_path_zip)

        downloaded_files = {
            'webbl': [],
            'aperak': [],
            'contrlx': [],
            'webbl_pdf':[]
        }

        xml_pdf_map = {}
        # to_delete = []
        files = zip_files
        for file in files:
            if file in ['webbl', 'vgm']:
                continue
            if file in already_downloaded:
                continue

            if file in xml_files:
                full_remote = f"{remote_path_xml}{file}"
            elif file in zip_files:
                full_remote = f"{remote_path_zip}{file}"
            else:
                continue

            full_local = os.path.join(local_path, file)

            try:
                # Download file
                sftp.get(full_remote, full_local)
                frappe.logger().info(f"⬇️ Downloaded: {file}")

                # Log download
                frappe.get_doc({
                    "doctype": "SFTP Downloaded Files",
                    "file_name": file,
                    "downloaded_on": today,
                    "downloaded_time": datetime.now().time(),
                    "file_type": file[:12] if len(file) >= 12 else file,
                    "file_path": full_local,
                    "status": "Downloaded"
                }).insert(ignore_permissions=True)
                frappe.db.commit()

                # Process XML file
                if file.endswith(".xml"):
                    prefix = file[:12]
                    if prefix == "WESEL_IFTMCS":
                        downloaded_files['webbl'].append(file)
                    elif prefix == "WESEL_APERAK":
                        downloaded_files['aperak'].append(full_local)
                    elif prefix == "WESEL_CONTRL":
                        downloaded_files['contrlx'].append(full_local)

                elif file.endswith(".zip"):
                    xml_pdf_map = {} 
                    with zipfile.ZipFile(full_local, 'r') as zip_ref:
                        zip_ref.extractall(local_path)
                        extracted_files = zip_ref.namelist()
                        if extracted_files[0].endswith(".XML") or extracted_files[0].endswith(".xml"):
                            xml_pdf_map[extracted_files[0]] = extracted_files[-1]
                        elif extracted_files[-1].endswith(".XML") or extracted_files[-1].endswith(".xml"):
                            xml_pdf_map[extracted_files[-1]] = extracted_files[0]
                        if xml_pdf_map:
                            downloaded_files['webbl_pdf'].append(xml_pdf_map)
                             

            except Exception as e:
                error_msg = f"❌ Failed downloading {file}: {str(e)}"
                frappe.log_error("SFTP File Download Error", error_msg)
                try:
                    if file.endswith((".zip", ".xml")):
                        frappe.get_doc({
                            "doctype": "SFTP Downloaded Files",
                            "file_name": file,
                            "downloaded_on": today,
                            "downloaded_time": datetime.now().time(),
                            "file_type": file[:12] if len(file) >= 12 else file,
                            "file_path": full_local if 'full_local' in locals() else "",
                            "status": "Failed"
                        }).insert(ignore_permissions=True)
                        frappe.db.commit()
                except:
                    pass

        processing_results = {
            'webbl_processed': 0,
            'aperak_processed': 0,
            'contrlx_processed': 0,
            'errors': []
        }

        if downloaded_files['webbl']:
            try:
                process_webbl_exml_files(downloaded_files['webbl'])
                processing_results['webbl_processed'] = len(downloaded_files['webbl'])
            except Exception as e:
                msg = f"❌ Failed processing WESEL_IFTMCS: {str(e)}"
                frappe.log_error("WESEL_IFTMCS Error", msg)
                processing_results['errors'].append(msg)
        
        for file_path in downloaded_files['aperak']:
            try:
                process_aperak_file(file_path, os.path.basename(file_path))
                processing_results['aperak_processed'] += 1
            except Exception as e:
                msg = f"❌ Failed processing APERAK {file_path}: {str(e)}"
                frappe.log_error("APERAK Error", msg)
                processing_results['errors'].append(msg)

        for file_path in downloaded_files['contrlx']:
            try:
                process_contrlx_file(file_path, os.path.basename(file_path))
                processing_results['contrlx_processed'] += 1
            except Exception as e:
                msg = f"❌ Failed processing CONTRLX {file_path}: {str(e)}"
                frappe.log_error("CONTRLX Error", msg)
                processing_results['errors'].append(msg)
        if xml_pdf_map:
            try:
                process_webbl_pdf_files(xml_pdf_map)
                processing_results['webbl_pdf_processed'] = len(downloaded_files['webbl_pdf'])
            except Exception as e:
                msg = f"❌ Failed processing webbl ZIP file: {str(e)}"
                frappe.log_error("webbl zip file Error", msg)
                processing_results['errors'].append(msg)

        frappe.logger().info("\u2705 Scheduled job completed successfully")

        # for remote_file in to_delete:
        #     try:
        #         sftp.remove(remote_file)
        #         print(f"Deleted from SFTP: {remote_file}")
        #     except Exception as e:
        #         frappe.log_error("SFTP Delete Error", f"Could not delete {remote_file}: {str(e)}")
        return {
            "status": "success",
            "message": "Files downloaded and processed successfully.",
            "details": processing_results
        }
        

    except Exception as e:
        error_msg = f"❌ SFTP Job Error: {str(e)}\n{traceback.format_exc()}"
        frappe.log_error("SFTP Download General Error", error_msg)
        return {"status": "error", "message": error_msg}

    finally:
        print("Closing SFTP connection...")
        try:
            if sftp:
                sftp.close()
        except:
            pass
        try:
            if transport:
                transport.close()
        except:
            pass





def process_aperak_file(file_path, file_name):
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()

        carrier_status = root.findtext(".//ShipmentComments[@CommentType='General']")
        raw_date = root.findtext(".//DateTime[@DateType='StatusChange']")
        shipment_id = root.findtext(".//ShipmentIdentifier")
        booking_number = root.findtext(".//ReferenceInformation[@ReferenceType='BookingNumber']")

        status_change_datetime = get_datetime(raw_date) if raw_date else None
        formatted_date = status_change_datetime.strftime("%d-%m-%Y") if status_change_datetime else None

        frappe.logger().info(f"APERAK | Shipment ID: {shipment_id} | Carrier Status: {carrier_status} | Date: {formatted_date}")

        existing_doc_name = frappe.db.exists("Inttra Status SI", {"shipping_instruction": shipment_id})
        if existing_doc_name:
            doc = frappe.get_doc("Inttra Status SI", existing_doc_name)
            doc.carrier_booking_number = booking_number
            doc.si_carrier_status = carrier_status
            doc.si_carrier_status_date = status_change_datetime
            doc.save(ignore_permissions=True)
        else:
            frappe.get_doc({
                "doctype": "Inttra Status SI",
                "shipping_instruction": shipment_id,
                "carrier_booking_number": booking_number,
                "si_carrier_status": carrier_status,
                "si_carrier_status_date": status_change_datetime
            }).insert(ignore_permissions=True)

        frappe.db.set_value("SFTP Downloaded Files", {"file_name": file_name}, "status", "Processed")
        frappe.db.commit()

    except Exception as e:
        frappe.log_error("Failed processing APERAK file", str(e))
        raise


def process_contrlx_file(file_path, file_name):
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()

        doc_identifier = None
        shipment_id = None
        raw_date = None
        inttra_status = None

        header = root.find("Header")
        if header is not None:
            doc_identifier = header.findtext("DocumentIdentifier")

        props = root.find(".//MessageBody/MessageProperties")
        if props is not None:
            shipment_id = props.findtext("ShipmentIdentifier")

            date_node = props.find("DateTime")
            if date_node is not None and date_node.attrib.get("DateType") == "Document":
                raw_date = date_node.text

            comments = props.find(".//Instructions/ShipmentComments[@CommentType='General']")
            if comments is not None:
                inttra_status = comments.text

        status_change_datetime = get_datetime(raw_date) if raw_date else None
        formatted_date = status_change_datetime.strftime("%d-%m-%Y") if status_change_datetime else None

        frappe.logger().info(f"CONTRLX | Shipment ID: {shipment_id} | INTTRA Status: {inttra_status} | Date: {formatted_date}")

        if not shipment_id:
            shipment_id = doc_identifier

        if not (shipment_id or inttra_status):
            raise Exception(f"Essential fields missing in XML: shipment_id={shipment_id}, inttra_status={inttra_status}")

        existing_doc_name = frappe.db.exists("Inttra Status SI", {"shipping_instruction": shipment_id})
        if existing_doc_name:
            doc = frappe.get_doc("Inttra Status SI", existing_doc_name)
            doc.inttra_si = doc_identifier
            doc.si_inttra_status = inttra_status
            doc.si_inttra_status_date = status_change_datetime

            if inttra_status and inttra_status.lower() == "rejected":
                doc.si_carrier_status = "Rejected"
                doc.si_carrier_status_date = status_change_datetime

            doc.save(ignore_permissions=True)
        else:
            new_doc = {
                "doctype": "Inttra Status SI",
                "shipping_instruction": shipment_id,
                "inttra_si": doc_identifier,
                "si_inttra_status": inttra_status,
                "si_inttra_status_date": status_change_datetime,
            }

            if inttra_status and inttra_status.lower() == "rejected":
                new_doc["si_carrier_status"] = "Rejected"
                new_doc["si_carrier_status_date"] = status_change_datetime

            frappe.get_doc(new_doc).insert(ignore_permissions=True)

        frappe.db.set_value("SFTP Downloaded Files", {"file_name": file_name}, "status", "Processed")
        frappe.db.commit()

    except Exception as e:
        error_msg = f"{file_name}: {str(e)}"
        frappe.log_error("Failed processing CONTRLX file", error_msg)
        raise




@frappe.whitelist(allow_guest=True)
def download_xml_from_sftp_old():
    """Download XML files from SFTP server and process them"""
    sftp = None
    transport = None
    frappe.logger().info("✅ Cron job: download_xml_from_sftp ran")
    try:
        sftp_settings = frappe.get_doc("SFTP Server Settings")
        host = sftp_settings.host
        username = sftp_settings.username
        password = sftp_settings.get_password("password")
        port = sftp_settings.port or 22

        transport = paramiko.Transport((host, port))
        transport.connect(username=username, password=password)
        sftp = paramiko.SFTPClient.from_transport(transport)

        remote_path = "outbound/"
        local_path = frappe.utils.get_site_path("private", "sftp_downloads")
        os.makedirs(local_path, exist_ok=True)

        today = datetime.now().date()
        already_downloaded = set([
            d.file_name for d in frappe.get_all("SFTP Downloaded Files",
                                                filters={"downloaded_on": today},
                                                fields=["file_name"])
        ])

        print(f"Already downloaded files for today: {already_downloaded}")

        files = sftp.listdir(remote_path)
        downloaded_files = {
            'webbl': [],
            'aperak': [],
            'contrlx': []
        }

        to_delete = []

        for file in files:
            if file.endswith(".xml") and file not in already_downloaded:
                try:
                    full_remote = f"{remote_path}{file}"
                    full_local = os.path.join(local_path, file)

                    sftp.get(full_remote, full_local)
                    print(f"Downloaded: {file}")

                    prefix = file[:12] if len(file) >= 12 else file

                    frappe.get_doc({
                        "doctype": "SFTP Downloaded Files",
                        "file_name": file,
                        "downloaded_on": today,
                        "file_type": prefix,
                        "file_path": full_local,
                        "status": "Downloaded"
                    }).insert(ignore_permissions=True)
                    frappe.db.commit()

                    if prefix == "WESEL_IFTMCS":
                        downloaded_files['webbl'].append(file)
                    if prefix == "WESEL_APERAK":
                        downloaded_files['aperak'].append(full_local)
                    if prefix == "WESEL_CONTRL":
                        downloaded_files['contrlx'].append(full_local)

                    to_delete.append(full_remote) 

                except Exception as e:
                    error_msg = f"Failed downloading {file}: {str(e)}"
                    print(error_msg)
                    frappe.log_error("SFTP Download File Error", error_msg)
                    try:
                        frappe.get_doc({
                            "doctype": "SFTP Downloaded Files",
                            "file_name": file,
                            "downloaded_on": today,
                            "file_type": file[:12] if len(file) >= 12 else file,
                            "file_path": full_local if 'full_local' in locals() else "",
                            "status": "Failed"
                        }).insert(ignore_permissions=True)
                        frappe.db.commit()
                    except:
                        pass

        processing_results = {
            'webbl_processed': 0,
            'aperak_processed': 0,
            'contrlx_processed': 0,
            'errors': []
        }

        if downloaded_files['webbl']:
            try:
                process_webbl_exml_files(downloaded_files['webbl'])
                processing_results['webbl_processed'] = len(downloaded_files['webbl'])
            except Exception as e:
                error_msg = f"Failed processing WESEL_IFTMCS files: {str(e)}"
                processing_results['errors'].append(error_msg)
                frappe.log_error("WESEL_IFTMCS Processing Error", error_msg)

        for file_path in downloaded_files['aperak']:
            try:
                process_aperak_file(file_path, os.path.basename(file_path))
                processing_results['aperak_processed'] += 1
            except Exception as e:
                error_msg = f"Failed processing APERAK file {os.path.basename(file_path)}: {str(e)}"
                processing_results['errors'].append(error_msg)
                frappe.log_error("APERAK Processing Error", error_msg)

        for file_path in downloaded_files['contrlx']:
            try:
                process_contrlx_file(file_path, os.path.basename(file_path))
                processing_results['contrlx_processed'] += 1
            except Exception as e:
                error_msg = f"Failed processing CONTRLX file {os.path.basename(file_path)}: {str(e)}"
                processing_results['errors'].append(error_msg)
                frappe.log_error("CONTRLX Processing Error", error_msg)
                print("✅ Scheduled job ran at " + frappe.utils.now())
                frappe.logger().info("✅ Scheduled job ran at " + frappe.utils.now())

        for remote_file in to_delete:
            try:
                sftp.remove(remote_file)
                print(f"Deleted from SFTP: {remote_file}")
            except Exception as e:
                frappe.log_error("SFTP Delete Error", f"Could not delete {remote_file}: {str(e)}")


        return {
            "status": "success",
            "message": "Files downloaded, processed, and deleted successfully from SFTP.",
            "details": processing_results
        }

    except Exception as e:
        error_msg = f"SFTP Download Error: {str(e)}\n{traceback.format_exc()}"
        frappe.log_error("SFTP Download General Error", error_msg)
        return {"status": "error", "message": f"Failed to download files from SFTP: {str(e)}"}

    finally:
        print("Closing SFTP connections...")
        try:
            if sftp:
                sftp.close()
        except:
            pass
        try:
            if transport:
                transport.close()
        except:
            pass




@frappe.whitelist(allow_guest=True)
def download_xml_from_sftp_archive():
    """Download XML and ZIP files from SFTP server and process them"""
    sftp = None
    transport = None
    frappe.logger().info("\u2705 Cron job: download_xml_from_sftp ran")
    try:
        # Setup SFTP connection
        xml_path = frappe.form_dict.get("xml_path")
        zip_path = frappe.form_dict.get("zip_path")

        if not xml_path or not zip_path:
            frappe.throw("Both xml_path and zip_path are required")

        sftp_settings = frappe.get_doc("SFTP Server Settings")
        host = sftp_settings.host
        username = sftp_settings.username
        password = sftp_settings.get_password("password")
        port = sftp_settings.port or 22

        transport = paramiko.Transport((host, port))
        transport.connect(username=username, password=password)
        sftp = paramiko.SFTPClient.from_transport(transport)

        # Define remote paths
        remote_path_xml = xml_path
        remote_path_zip = zip_path
        
        local_path = frappe.utils.get_site_path("private", "sftp_downloads")
        os.makedirs(local_path, exist_ok=True)
        today = datetime.now().date()
        already_downloaded = set([
            d.file_name for d in frappe.get_all("SFTP Downloaded Files", 
                                                filters={"downloaded_on": today}, 
                                                fields=["file_name"])
        ])

        # List files from both paths
        xml_files = sftp.listdir(remote_path_xml)
        zip_files = sftp.listdir(remote_path_zip)
        files = xml_files + zip_files

        

        downloaded_files = {
            'webbl': [],
            'aperak': [],
            'contrlx': [],
            'webbl_pdf':[]
        }
        xml_pdf_map = {}
        for file in files:
            if file in ['webbl', 'vgm']:
                continue
            if file in already_downloaded:
                continue

            if file in xml_files:
                full_remote = f"{remote_path_xml}{file}"
            elif file in zip_files:
                full_remote = f"{remote_path_zip}{file}"
            else:
                continue

            full_local = os.path.join(local_path, file)

            try:
                # Download file
                sftp.get(full_remote, full_local)
                frappe.logger().info(f"⬇️ Downloaded: {file}")

                # Log download
                frappe.get_doc({
                    "doctype": "SFTP Downloaded Files",
                    "file_name": file,
                    "downloaded_on": today,
                    "downloaded_time": datetime.now().time(),
                    "file_type": file[:12] if len(file) >= 12 else file,
                    "file_path": full_local,
                    "status": "Downloaded"
                }).insert(ignore_permissions=True)
                frappe.db.commit()

                # Process XML file
                if file.endswith(".xml"):
                    prefix = file[:12]
                    if prefix == "WESEL_IFTMCS":
                        downloaded_files['webbl'].append(file)
                    elif prefix == "WESEL_APERAK":
                        downloaded_files['aperak'].append(full_local)
                    elif prefix == "WESEL_CONTRL":
                        downloaded_files['contrlx'].append(full_local)

                elif file.endswith(".zip"):
                    xml_pdf_map = {} 
                    with zipfile.ZipFile(full_local, 'r') as zip_ref:
                        zip_ref.extractall(local_path)
                        extracted_files = zip_ref.namelist()
                        if extracted_files[0].endswith(".XML") or extracted_files[0].endswith(".xml"):
                            xml_pdf_map[extracted_files[0]] = extracted_files[-1]
                        elif extracted_files[-1].endswith(".XML") or extracted_files[-1].endswith(".xml"):
                            xml_pdf_map[extracted_files[-1]] = extracted_files[0]
                        if xml_pdf_map:
                            downloaded_files['webbl_pdf'].append(xml_pdf_map)
                        print(downloaded_files['webbl_pdf'],'downloaded_files')    

            except Exception as e:
                error_msg = f"❌ Failed downloading {file}: {str(e)}"
                frappe.log_error("SFTP File Download Error", error_msg)
                try:
                    frappe.get_doc({
                        "doctype": "SFTP Downloaded Files",
                        "file_name": file,
                        "downloaded_on": today,
                        "downloaded_time": datetime.now().time(),
                        "file_type": file[:12] if len(file) >= 12 else file,
                        "file_path": full_local if 'full_local' in locals() else "",
                        "status": "Failed"
                    }).insert(ignore_permissions=True)
                    frappe.db.commit()
                except:
                    pass

        processing_results = {
            'webbl_processed': 0,
            'aperak_processed': 0,
            'contrlx_processed': 0,
            'errors': []
        }

        if downloaded_files['webbl']:
            try:
                process_webbl_exml_files(downloaded_files['webbl'])
                processing_results['webbl_processed'] = len(downloaded_files['webbl'])
            except Exception as e:
                msg = f"❌ Failed processing WESEL_IFTMCS: {str(e)}"
                frappe.log_error("WESEL_IFTMCS Error", msg)
                processing_results['errors'].append(msg)
        

        for file_path in downloaded_files['aperak']:
            try:
                process_aperak_file(file_path, os.path.basename(file_path))
                processing_results['aperak_processed'] += 1
            except Exception as e:
                msg = f"❌ Failed processing APERAK {file_path}: {str(e)}"
                frappe.log_error("APERAK Error", msg)
                processing_results['errors'].append(msg)

        for file_path in downloaded_files['contrlx']:
            try:
                process_contrlx_file(file_path, os.path.basename(file_path))
                processing_results['contrlx_processed'] += 1
            except Exception as e:
                msg = f"❌ Failed processing CONTRLX {file_path}: {str(e)}"
                frappe.log_error("CONTRLX Error", msg)
                processing_results['errors'].append(msg)
        
        if xml_pdf_map:
            try:
                process_webbl_pdf_files(xml_pdf_map)
                processing_results['webbl_pdf_processed'] = len(downloaded_files['webbl_pdf'])
            except Exception as e:
                msg = f"❌ Failed processing webbl ZIP file: {str(e)}"
                frappe.log_error("webbl zip file Error", msg)
                processing_results['errors'].append(msg)

        frappe.logger().info("\u2705 Scheduled job completed successfully")
        return {
            "status": "success",
            "message": "Files downloaded and processed successfully.",
            "details": processing_results
        }

    except Exception as e:
        error_msg = f"❌ SFTP Job Error: {str(e)}\n{traceback.format_exc()}"
        frappe.log_error("SFTP Download General Error", error_msg)
        return {"status": "error", "message": error_msg}

    finally:
        print("Closing SFTP connection...")
        try:
            if sftp:
                sftp.close()
        except:
            pass
        try:
            if transport:
                transport.close()
        except:
            pass

# @frappe.whitelist(allow_guest=True)
# def enqueue_sftp_watcher():
#     frappe.enqueue(
#         "westside.api.sftp_file_dwnld.start_sftp_watcher",
#         queue="long",
#         timeout=0
#     )
